async function loadComponent(id, filename, callback) {
  const element = document.getElementById(id);
  if (!element) return;

  const currentPath = window.location.pathname;
  const isInViewsFolder = currentPath.includes("/Frontend/views/");

  const primaryPath = isInViewsFolder ? filename : "Frontend/views/" + filename;
  const secondaryPath = isInViewsFolder ? "Frontend/views/" + filename : filename;

  try {
    let res = await fetch(primaryPath);
    if (!res.ok) throw new Error();

    const html = await res.text();
    element.innerHTML = html;

    if (id === "header") ajustarRutasBase();
    if (callback) callback();

  } catch {
    try {
      const res = await fetch(secondaryPath);
      if (!res.ok) throw new Error();

      const html = await res.text();
      element.innerHTML = html;

      if (id === "header") ajustarRutasBase();
      if (callback) callback();

    } catch (error) {
      console.error(`No se pudo cargar el componente "${filename}" desde ninguna ruta.`);
    }
  }
}

function ajustarRutasBase() {
  const basePath = window.location.pathname.includes("/Frontend/views/") ? "../../" : "";

  document.querySelectorAll("[data-path]").forEach(link => {
    const ruta = link.getAttribute("data-path");
    link.setAttribute("href", basePath + ruta);
  });
}

document.addEventListener("DOMContentLoaded", () => {
  loadComponent("header", "header.html", () => {
    if (typeof initNavbar === 'function') {
      initNavbar();
    }
  });

  loadComponent("footer", "footer.html");
});
