<?php
function registrarVisita($archivo = __DIR__ . '/log_estadistica.json') {
    $visitas = 0;

    if (file_exists($archivo)) {
        $contenido = file_get_contents($archivo);
        $datos = json_decode($contenido, true);

        if ($datos !== null && is_array($datos)) {
            foreach ($datos as $entrada) {
                if (isset($entrada['visitas'])) {
                    $visitas = $entrada['visitas'];
                    break;
                }
            }
        }
    }

    $visitas++;

    // Nueva entrada
    $logEntry = [
        "fecha" => date("Y-m-d H:i:s"),
        "Acción" => "vista a la landig page match creativo",
        "visitas" => $visitas
    ];

    file_put_contents($archivo, json_encode([$logEntry], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    
    return $logEntry;
}

header('Content-Type: application/json');
$logEntry = registrarVisita();
echo json_encode([
    "exito" => true,
    "visitas" => $logEntry['visitas'],
    "fecha" => $logEntry['fecha']
]);