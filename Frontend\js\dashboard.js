function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
    return null;
}

document.addEventListener('DOMContentLoaded', function () {
    const token = getCookie('authToken');
    const userName = getCookie('userName') ? decodeURIComponent(getCookie('userName')) : "Usuario";
    const userEmail = getCookie('userEmail') ? decodeURIComponent(getCookie('userEmail')) : "<EMAIL>";

    if (!token) {
        window.location.href = 'login.html';
        return;
    }

    // Actualizar información del usuario en la página
    document.getElementById('user-name').textContent = userName;
    document.getElementById('user-email').textContent = userEmail;
    document.getElementById('welcome-user').textContent = userName;

    // Obtener visitas
    fetch(`${window.API_URL_PHP}controller/read_visits.php`)
        .then(res => res.json())
        .then(data => {
            if (data.exito) {
                document.getElementById('visitas-landing').textContent = data.visitas;
                document.getElementById('fecha-landing').textContent = data.fecha;
            } else {
                document.getElementById('visitas-landing').textContent = 'Error';
                document.getElementById('fecha-landing').textContent = 'Error';
            }
        })
        .catch(() => {
            document.getElementById('visitas-landing').textContent = 'Error de conexión';
            document.getElementById('fecha-landing').textContent = 'Error de conexión';
        });
});

// Manejar cierre de sesión
document.getElementById('logout-btn').addEventListener('click', function () {
    document.cookie = 'authToken=; path=/; max-age=0';
    document.cookie = 'userName=; path=/; max-age=0';
    document.cookie = 'userEmail=; path=/; max-age=0';
    document.cookie = 'userId=; path=/; max-age=0';
    window.location.href = 'login.html';
});