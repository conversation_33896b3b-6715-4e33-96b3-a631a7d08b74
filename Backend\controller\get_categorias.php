<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: application/json'); 
include '../models/conexion.php';

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $stmt = $conn->prepare("SELECT * FROM categorias ORDER BY nombre_categoria");
    $stmt->execute();
    $categorias = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'exito' => true,
        'categorias' => $categorias
    ]);
    
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'exito' => false, 
        'error' => 'Error al obtener las categorías: ' . $e->getMessage()
    ]);
}?>

