<?php
header('Content-Type: application/json');
require_once __DIR__ . '/../models/conexion.php';

try {
    $stmt = $conn->prepare("SELECT c.*, p.nombre AS nombre_creativo, p.apellido AS apellido_creativo, c.estado FROM contactos c LEFT JOIN perfil_creativo p ON c.id_perfil_creativo = p.id_pcreativa");
    $stmt->execute();
    $contactos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo json_encode($contactos);
} catch (PDOException $e) {
    echo json_encode([]);
} 