document.addEventListener('DOMContentLoaded', function () {
  const contactoForm = document.getElementById('contacto-form');
  const mensajeContacto = document.getElementById('mensaje-contacto');
  const cerrarPopup = document.querySelector('.cerrar-popup');
  const btnContactar = document.getElementById('btn-contactar');
  const popupContacto = document.getElementById('contacto-popup');
  const popupDatos = document.getElementById('popup-datos-contacto');
  const cerrarDatosBtn = document.getElementById('cerrar-datos-contacto');
  const datosContactoInfo = document.getElementById('datos-contacto-info');
  const btnEnviar = contactoForm.querySelector('.btn-enviar');

 
  if (cerrarPopup) {
    cerrarPopup.addEventListener('click', function () {
      popupContacto.style.display = 'none';
    });
  }

  if (btnContactar) {
    btnContactar.addEventListener('click', function () {
      const urlParams = new URLSearchParams(window.location.search);
      const idPerfilCreativo = urlParams.get('id') || '1';
      document.getElementById('id_perfil_creativo').value = idPerfilCreativo;
      popupContacto.style.display = 'flex';
    });
  }

  if (cerrarDatosBtn) {
    cerrarDatosBtn.addEventListener('click', function () {
      popupDatos.style.display = 'none';
    });
  }

  if (contactoForm) {
    contactoForm.addEventListener('submit', async function (e) {
      e.preventDefault();

      const descripcionInput = document.getElementById('descripcion');
      if (!descripcionInput.value.trim()) {
        mostrarMensaje('La descripción es obligatoria', false);
        descripcionInput.focus();
        return;
      }

      // Desactivar botón y mostrar estado
      btnEnviar.disabled = true;
      btnEnviar.textContent = 'Enviando...';

      try {
        const formData = {
          nombre: document.getElementById('nombre').value,
          telefono: document.getElementById('telefono').value,
          email: document.getElementById('email').value,
          descripcion: descripcionInput.value,
          fechainicio: document.getElementById('fechainicio').value,
          fechatermino: document.getElementById('fechatermino').value,
          id_perfil_creativo: document.getElementById('id_perfil_creativo').value
        };

        const response = await fetch('http://localhost/match-creativo/Backend/models/create_contacto_tkn.php', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
          });

        const data = await response.json();

        if (data.exito) {
          contactoForm.reset();
          popupContacto.style.display = 'none';

          const estadoDisponible = document.getElementById('estado-disponibilidad')?.getAttribute('data-status') === 'true';
          console.log('estadoDisponible:', estadoDisponible);

          if (estadoDisponible) {
            datosContactoInfo.innerHTML = `
              <p><strong>Teléfono:</strong> ${data.contacto.telefono}</p>
              <p><strong>Email:</strong> ${data.contacto.email}</p>
            `;
          } else {
            datosContactoInfo.innerHTML = `<div class="mensaje-no-disponible">
              Este creativo actualmente no se encuentra disponible para compartir sus datos de contacto.</div>
              <div id="recomendaciones-container"><p>Cargando recomendaciones...</p></div>`;

            fetch(
              'http://localhost/match-creativo/Backend/controller/get_creativos_disponibles.php'
            )
              .then((res) => res.json())
              .then((data) => {
                const contenedor = document.getElementById('recomendaciones-container');
                if (data.exito && data.creativos.length > 0) {
                  contenedor.innerHTML =
                    '<h4>Contacta con nuestros creativos disponibles:</h4>';
                  data.creativos.forEach((creativo) => {
                    let imgSrc = '../assets/default-profile.jpg';
                    if (creativo.foto_perfil) imgSrc = creativo.foto_perfil;

                    const card = document.createElement('div');
                    card.className = 'card-creativo';
                    card.innerHTML = `
                      <img src="${imgSrc}" alt="${creativo.nombre} ${creativo.apellido}" class="foto-creativo">
                      <div>
                        <strong>${creativo.nombre} ${creativo.apellido}</strong><br>
                        <small>${creativo.titulo_profesional}</small><br>
                        <button onclick="window.location.href='perfil_creativo.html?id=${creativo.id_pcreativa}'"
                          class="btn-ver-perfil">Ver Perfil</button>
                      </div>`;
                    contenedor.appendChild(card);
                  });
                } else {
                  contenedor.innerHTML = `<p>No hay otros creativos disponibles en este momento.</p>`;
                }
              })
              .catch((error) => {
                console.error('Error cargando recomendaciones:', error);
                const contenedor = document.getElementById('recomendaciones-container');
                contenedor.innerHTML = `<p>No se pudieron cargar recomendaciones en este momento.</p>`;
              });
          }
          popupDatos.style.display = 'flex';
        } else {
          mostrarMensaje('Error: ' + data.error, false);
        }
      } catch (error) {
        mostrarMensaje('Error de conexión', false);
        console.error('Error:', error);
      } finally {
        // Restaurar botón
        btnEnviar.disabled = false;
        btnEnviar.textContent = 'Enviar';
      }
    });
  }

  function mostrarMensaje(mensaje, esExito) {
    mensajeContacto.innerHTML = mensaje;
    mensajeContacto.style.display = 'block';
    mensajeContacto.className = 'mensaje-container ' + (esExito ? 'mensaje-exito' : 'mensaje-error');
  }
});
