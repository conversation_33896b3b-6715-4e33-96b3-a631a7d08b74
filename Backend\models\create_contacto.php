<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: application/json'); 

require_once __DIR__ . '/conexion.php';

use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

require_once __DIR__ . '/../../vendor/autoload.php';  
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../../'); 
$dotenv->load();

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$data = json_decode(file_get_contents("php://input"), true);

try {
    if (!isset($data['id_perfil_creativo'])) {
        throw new Exception("Falta id_perfil_creativo en los datos recibidos");
    }

    
    $stmt = $conn->prepare("INSERT INTO contactos (nombre, telefono, email, descripcion, fechainicio, fechatermino, id_perfil_creativo) 
                           VALUES (?, ?, ?, ?, ?, ?, ?)");
    
    $stmt->execute([
        $data['nombre'],
        $data['telefono'],
        $data['email'],
        $data['descripcion'],
        $data['fechainicio'] ?: null,
        $data['fechatermino'] ?: null,
        $data['id_perfil_creativo']
    ]);
    
    $contacto_id = $conn->lastInsertId();

    
    $stmt2 = $conn->prepare("SELECT correo, telefono, estado_perfil FROM perfil_creativo WHERE id_pcreativa = ?");
    $stmt2->execute([$data['id_perfil_creativo']]);
    $perfil = $stmt2->fetch(PDO::FETCH_ASSOC);

    if (!$perfil) {
        throw new Exception("No se encontró el perfil creativo");
    }

    $correo_destino = $perfil['correo'];

    
    if ((int)$perfil['estado_perfil'] === 1) {
        $mail = new PHPMailer(true);
        try {
            $mail->isSMTP();
            $mail->CharSet = "UTF-8";
            $mail->Host       = $_ENV['MAIL_HOST'];
            $mail->SMTPAuth   = true;
            $mail->Username   = $_ENV['MAIL_USERNAME'];
            $mail->Password   = $_ENV['MAIL_PASSWORD'];
            $mail->SMTPSecure = 'tls';
            $mail->Port       = $_ENV['MAIL_PORT'];
    
            $mail->setFrom($_ENV['MAIL_FROM'], $_ENV['MAIL_FROM_NAME']);
            $mail->addAddress($correo_destino);
    
            $mail->isHTML(true);
            $mail->Subject = "Nuevo contacto de " . htmlspecialchars($data['nombre']);
            $mail->Body    = "
                <h2>Nuevo mensaje recibido</h2>
                <p><strong>Nombre:</strong> " . htmlspecialchars($data['nombre']) . "</p>
                <p><strong>Email:</strong> " . htmlspecialchars($data['email']) . "</p>
                <p><strong>WhatsApp:</strong> " . htmlspecialchars($data['telefono']) . "</p>
                <p><strong>Descripción:</strong> " . nl2br(htmlspecialchars($data['descripcion'])) . "</p>
                <p><strong>Fecha inicio:</strong> " . htmlspecialchars($data['fechainicio']) . "</p>
                <p><strong>Fecha término:</strong> " . htmlspecialchars($data['fechatermino']) . "</p>
            ";
    
            $mail->send();
    
        } catch (Exception $e) {
            echo json_encode([
                'exito' => false,
                'error' => "Error enviando correo: " . $mail->ErrorInfo
            ]);
            exit;
        }
    }

    
    echo json_encode([
        'exito' => true,
        'mensaje' => 'Contacto enviado correctamente y correo notificado',
        'id' => $contacto_id,
        'contacto' => [
            'email' => $perfil['correo'],
            'telefono' => $perfil['telefono']
        ]
    ]);

} catch(Exception $e) {
    http_response_code(500);
    echo json_encode([
        'exito' => false,
        'error' => 'Error: ' . $e->getMessage()
    ]);
}
