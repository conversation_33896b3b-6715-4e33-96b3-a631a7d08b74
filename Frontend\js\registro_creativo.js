document.addEventListener('DOMContentLoaded', function() {
    cargarCategorias();
    
    // Establecer fecha actual como valor predeterminado para startdate
    document.getElementById('startdate').valueAsDate = new Date();
    
    const form = document.getElementById('registro-form');
    form.addEventListener('submit', registrarCreativo);
});

async function registrarCreativo(event) {
    event.preventDefault();
    
    try {
        // Procesar la foto de perfil
        let fotoPerfil = '';
        const inputFoto = document.getElementById('foto_perfil');
        
        if (inputFoto.files && inputFoto.files[0]) {
            const file = inputFoto.files[0];
            fotoPerfil = await convertirImagenABase64(file);
        }
        
        // Obtener valores de fecha
        const startdate = document.getElementById('startdate').value;
        let enddate = document.getElementById('enddate').value;
        
        // Si enddate está vacío, establecer como null
        if (!enddate) {
            enddate = null;
        }
        
        // Crear objeto con los datos del perfil creativo
        const datosCreativo = {
            id_pcreativa: generarIdUnico(),
            nombre: document.getElementById('nombre').value,
            apellido: document.getElementById('apellido').value,
            titulo_profesional: document.getElementById('titulo_profesional').value,
            telefono: document.getElementById('telefono').value,
            id_categoria: document.getElementById('id_categoria').value,
            descripcion: document.getElementById('descripcion').value,
            experiencia: document.getElementById('experiencia').value,
            foto_perfil: fotoPerfil,
            startdate: startdate,
            enddate: enddate,
            correo: document.getElementById('correo_electronico').value
        };
        
        // Enviar datos al servidor
        const responseCreativo = await fetch('https://test.matchcreativo.cl/Backend/models/create_user_creativos.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(datosCreativo)
        });
        
        const dataCreativo = await responseCreativo.json();
        
        if (dataCreativo.exito) {
            mostrarMensaje('¡Perfil creativo registrado con éxito!', true);
            document.getElementById('registro-form').reset();
            
            setTimeout(() => {
                window.location.href = '../../index.html';
            }, 2000);
        } else {
            mostrarMensaje('Error al crear perfil creativo: ' + dataCreativo.error, false);
        }
        
    } catch (error) {
        mostrarMensaje('Error de conexión', false);
        console.error('Error:', error);
    }
}

function mostrarMensaje(mensaje, esExito) {
    const mensajeContainer = document.getElementById('mensaje');
    mensajeContainer.textContent = mensaje;
    mensajeContainer.className = 'mensaje-container';
    
    if (esExito) {
        mensajeContainer.classList.add('mensaje-exito');
    } else {
        mensajeContainer.classList.add('mensaje-error');
    }
    
    mensajeContainer.scrollIntoView({ behavior: 'smooth' });
}

function generarIdUnico() {
    return 'PC' + Date.now().toString().slice(-8) + Math.floor(Math.random() * 1000);
}

// Función para convertir una imagen a base64
function convertirImagenABase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = error => reject(error);
    });
}



