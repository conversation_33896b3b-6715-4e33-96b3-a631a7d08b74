 :root {
      --primary: #ff6b35;
      --primary-dark: #ff5722;
      --primary-light: #ff8a65;
      --secondary: #2d3436;
      --accent: #ffe0d6;
      --gradient: linear-gradient(135deg, var(--primary) 0%, #ff9f43 100%);
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Space Grotesk', sans-serif;
    }

    body {
      background-color: #0a0a0a;
      color: #fff;
      overflow-x: hidden;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    /* Hero Section */
    .hero {
      min-height: 100vh;
      position: relative;
      display: flex;
      align-items: center;
      background: #0a0a0a;
      overflow: hidden;
    }

    .hero-grid {
      position: absolute;
      inset: 0;
      background-size: 50px 50px;
      background-image: 
        linear-gradient(to right, rgba(255,255,255,0.05) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(255,255,255,0.05) 1px, transparent 1px);
      transform: perspective(1000px) rotateX(60deg);
      transform-origin: center top;
      animation: grid 20s linear infinite;
    }

    @keyframes grid {
      to {
        background-position: 0 -50px;
      }
    }

    .hero-content {
      position: relative;
      z-index: 2;
      max-width: 800px;
      margin: 0 auto;
      text-align: center;
    }

    .hero-title {
      font-size: 7rem;
      font-weight: 700;
      line-height: 1;
      margin-bottom: 2rem;
      background: var(--gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      opacity: 0;
      filter: blur(10px);
      transform: scale(0.8);
    }

    .hero-subtitle {
      font-size: 1.5rem;
      color: rgba(255,255,255,0.8);
      margin-bottom: 3rem;
      opacity: 0;
      transform: translateY(20px);
    }

    .stats-container {
      display: flex;
      justify-content: center;
      gap: 4rem;
      margin: 4rem 0;
    }

    .stat {
      opacity: 0;
      transform: translateY(20px);
    }

    .stat-number {
      font-size: 4rem;
      font-weight: 700;
      color: var(--primary);
      line-height: 1;
    }

    .stat-label {
      color: rgba(255,255,255,0.6);
      font-size: 1.1rem;
      margin-top: 0.5rem;
    }

    .cta-button {
      display: inline-flex;
      align-items: center;
      gap: 1rem;
      background: var(--gradient);
      color: white;
      padding: 1.2rem 3rem;
      border-radius: 100px;
      font-weight: 600;
      font-size: 1.2rem;
      text-decoration: none;
      transition: all 0.3s;
      opacity: 0;
      transform: translateY(20px);
      position: relative;
      overflow: hidden;
    }

    .cta-button::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      transform: translateX(-100%);
      transition: transform 0.6s;
    }

    .cta-button:hover::before {
      transform: translateX(100%);
    }

    /* Vision Section */
    .vision {
      padding: 10rem 0;
      background: #111;
      position: relative;
    }

    .vision-title {
      font-size: 4rem;
      text-align: center;
      margin-bottom: 6rem;
      color: white;
      opacity: 0;
      transform: translateY(30px);
    }

    .vision-cards {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 2rem;
    }

    .vision-card {
      background: rgba(255,255,255,0.03);
      border: 1px solid rgba(255,255,255,0.1);
      padding: 3rem;
      border-radius: 20px;
      transition: all 0.5s;
      opacity: 0;
      transform: translateY(30px);
    }

    .vision-card:hover {
      background: rgba(255,255,255,0.05);
      transform: translateY(-10px);
    }

    .vision-card h3 {
      font-size: 2rem;
      color: var(--primary);
      margin-bottom: 1.5rem;
    }

    .vision-card p {
      color: rgba(255,255,255,0.7);
      line-height: 1.6;
    }
       
    /* About Section */
    .about {
      padding: 10rem 0;
      background: #0a0a0a;
      position: relative;
      overflow: hidden;
    }

    .about-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 6rem;
      align-items: center;
    }

    .about-text {
      opacity: 0;
      transform: translateX(-30px);
    }

    .about-text p {
      color: rgba(255,255,255,0.7);
      font-size: 1.2rem;
      line-height: 1.8;
      margin-bottom: 1.5rem;
    }

    .about-image {
      position: relative;
      opacity: 0;
      transform: translateX(30px);
    }

    .about-image img {
      width: 100%;
      border-radius: 20px;
      filter: brightness(0.8) contrast(1.2);
    }

    .about-image::before {
      content: '';
      position: absolute;
      inset: -20px;
      border: 2px solid var(--primary);
      border-radius: 30px;
      opacity: 0.3;
      transition: all 0.3s;
    }

    .about-image:hover::before {
      inset: -10px;
      opacity: 0.6;
    }
    
     /* News Section */
    .news {
      padding: 10rem 0;
      background: #0a0a0a;
      position: relative;
      overflow: hidden;
    }

    .news-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 6rem;
      align-items: center;
    }

    .news-text {
      opacity: 0;
      transform: translateX(-30px);
    }

    .news-text p {
      color: rgba(255,255,255,0.7);
      font-size: 1.2rem;
      line-height: 1.8;
      margin-bottom: 1.5rem;
    }

    .news-image {
      position: relative;
      opacity: 0;
      transform: translateX(30px);
    }

    .news-image img {
      width: 100%;
      border-radius: 20px;
      filter: brightness(0.8) contrast(1.2);
    }

    .news-image::before {
      content: '';
      position: absolute;
      inset: -20px;
      border: 2px solid var(--primary);
      border-radius: 30px;
      opacity: 0.3;
      transition: all 0.3s;
    }

    .news-image:hover::before {
      inset: -10px;
      opacity: 0.6;
    }

    /* Sección de creativos */
    .creatives {
      padding: 10rem 0;
      background: #111;
      position: relative;
    }

    .creatives-title {
      font-size: 4rem;
      text-align: center;
      margin-bottom: 6rem;
      color: white;
      opacity: 0;
      transform: translateY(30px);
    }

    /* Carrusel de creativos */
    .carousel-container {
      position: relative;
      overflow: hidden;
      border-radius: 20px;
      width: 996px; /* 300px * 3 + 48px * 2 (gaps) */
      max-width: 100%;
      margin: 0 auto;
    }

    .creatives-grid {
      display: flex;
      transition: transform 0.5s ease;
      gap: 3rem;
      padding: 2rem 0;
    }

    .carousel-track {
      display: flex;
      gap: 3rem;
      transition: transform 0.5s ease;
      width: max-content;
      align-items: stretch;
    }

    /* Controles del carrusel */
    .carousel-controls {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 2rem;
      margin-top: 3rem;
    }

    .carousel-btn {
      background: rgba(255,255,255,0.1);
      border: 1px solid rgba(255,255,255,0.2);
      color: white;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 1.2rem;
    }

    .carousel-btn:hover {
      background: var(--primary);
      border-color: var(--primary);
      transform: scale(1.1);
    }

    .carousel-btn:disabled {
      opacity: 0.3;
      cursor: not-allowed;
      transform: none;
    }

    .carousel-btn:disabled:hover {
      background: rgba(255,255,255,0.1);
      border-color: rgba(255,255,255,0.2);
    }

    /* Indicadores del carrusel */
    .carousel-indicators {
      display: flex;
      justify-content: center;
      gap: 0.5rem;
    }

    .carousel-indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: rgba(255,255,255,0.3);
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .carousel-indicator.active {
      background: var(--primary);
      transform: scale(1.2);
    }

    /* Cards de creativos */
    .creative-card {
      background: rgba(255,255,255,0.03);
      border: 1px solid rgba(255,255,255,0.1);
      padding: 2rem;
      border-radius: 20px;
      transition: all 0.5s;
      width: 300px;
      flex-shrink: 0;
      opacity: 1 !important;
      transform: none !important;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      box-sizing: border-box;
    }

    .creative-card:hover {
      background: rgba(255,255,255,0.05);
      transform: translateY(-10px) scale(1.02);
    }

    .creative-image {
      width: 120px;
      height: 120px;
      margin: 0 auto 2rem;
      position: relative;
    }

    .creative-image img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 60px;
      filter: grayscale(100%) brightness(0.8);
      transition: all 0.5s;
    }

    .creative-card:hover .creative-image img {
      filter: grayscale(0%) brightness(1);
    }

    .creative-image::before {
      content: '';
      position: absolute;
      inset: -5px;
      border: 2px solid var(--primary);
      border-radius: 100px;
      opacity: 0.3;
      transition: all 0.3s;
    }

    .creative-card:hover .creative-image::before {
      opacity: 1;
      transform: scale(1.1);
    }

    .creative-name {
      font-size: 1.5rem;
      color: white;
      text-align: center;
      margin-bottom: 0.5rem;
    }

    .creative-role {
      color: var(--primary);
      text-align: center;
      font-size: 1rem;
      margin-bottom: 1.5rem;
    }

    .creative-quote {
      color: rgba(255,255,255,0.7);
      text-align: center;
      font-style: italic;
      line-height: 1.6;
    }

    @media (max-width: 1024px) {
      .hero-title {
        font-size: 5rem;
      }

      .vision-cards {
        grid-template-columns: repeat(2, 1fr);
      }

      /* Carrusel tablet - 2 cards */
      .carousel-container {
        width: 648px; /* 300px * 2 + 48px * 1 (gap) */
      }
    }

    @media (max-width: 768px) {
      .hero-title {
        font-size: 3.5rem;
      }

      .stats-container {
        flex-direction: column;
        gap: 2rem;
      }

      .vision-cards,
      .about-content,
      .news-content{
        grid-template-columns: 1fr;
      }

      .about-image,.news-content {
        order: -1;
      }

      /* Carrusel responsivo */
      .creatives-title {
        font-size: 2.5rem;
      }

      /* Carrusel móvil - 1 card */
      .carousel-container {
        width: 300px; /* 300px * 1 */
      }

      .creative-card {
        width: 300px;
        padding: 1.5rem;
      }

      .carousel-controls {
        gap: 1rem;
      }

      .carousel-btn {
        width: 40px;
        height: 40px;
        font-size: 1rem;
      }

      /* Navbar responsivo */
      .navbar-container {
        position: relative;
      }

      .navbar-toggle {
        display: flex !important;
        z-index: 1001;
        position: relative;
      }

      /* Animación del icono hamburguesa */
      .navbar-toggle.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
      }

      .navbar-toggle.active span:nth-child(2) {
        opacity: 0;
      }

      .navbar-toggle.active span:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
      }

      .navbar-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background-color: #111;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: left 0.3s ease;
        gap: 1.5rem;
      }

      .navbar-menu.active {
        left: 0;
      }

      .navbar-menu li {
        width: 100%;
        text-align: center;
      }

      .navbar-menu li a {
        display: block;
        padding: 1rem;
        font-size: 1.2rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }
    }
    
    .footer {
      background-color: #111;
      padding: 4rem 2rem 2.5rem; /* Más padding arriba para separar */
      color: rgba(255, 255, 255, 0.8);
      font-family: 'Space Grotesk', sans-serif;
      border-top: 3px solid var(--primary); /* Línea separadora */
      margin-top: 4rem; /* Margen adicional para que no quede pegado */
    }


    .footer-grid {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
      flex-wrap: wrap;
      gap: 1.5rem;
    }

    .footer-left h2 {
      color: var(--primary);
      font-size: 1.8rem;
      margin-bottom: 0.3rem;
    }

    .footer-left p {
      font-size: 0.9rem;
      color: rgba(255,255,255,0.6);
    }

    .footer-right p {
      margin: 0.2rem 0;
      font-size: 0.95rem;
    }

    .footer-right a {
      color: var(--primary);
      text-decoration: none;
      transition: color 0.3s ease;
    }

    .footer-right a:hover {
      color: var(--primary-dark);
      text-decoration: underline;
    }

    @media (max-width: 600px) {
      .footer-grid {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
      }

      .footer-right p {
        margin: 0.3rem 0;
      }
    }
    
    .navbar {
    position: sticky;
    top: 0;
    width: 100%;
    background-color: #111;
    border-bottom: 3px solid var(--primary);
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0,0,0,0.7);
    font-family: 'Space Grotesk', sans-serif;
    }

    .navbar-container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 1rem 2rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .navbar-logo {
      color: var(--primary);
      font-weight: 700;
      font-size: 1.8rem;
      cursor: pointer;
      user-select: none;
    }

    .navbar-menu {
      list-style: none;
      display: flex;
      gap: 2rem;
    }

    .navbar-menu li a {
      color: rgba(255, 255, 255, 0.8);
      text-decoration: none;
      font-weight: 500;
      transition: color 0.3s ease;
    }

    .navbar-menu li a:hover,
    .navbar-menu li a:focus {
      color: var(--primary);
    }

    /* Menú hamburguesa - oculto por defecto en desktop */
    .navbar-toggle {
      display: none;
      flex-direction: column;
      cursor: pointer;
      padding: 0.5rem;
      border-radius: 4px;
    }

    .navbar-toggle span {
      width: 25px;
      height: 3px;
      background-color: var(--primary);
      margin: 3px 0;
      transition: 0.3s;
      border-radius: 2px;
    }

    .btn-perfil {
      margin: 10px auto;
      padding: 8px 16px;
      background-color: #ff6b35; /* naranja */
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.3s ease;
      display: block;
      width: 80%;
      text-align: center;
    }

    .btn-perfil:hover {
      background-color: #ff5722; /* naranja más oscuro */
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(255, 107, 53, 0.3);
    }