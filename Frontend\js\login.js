document.addEventListener('DOMContentLoaded', function() {
  const loginF = document.querySelector("form");
  
  if (loginF) {
    loginF.addEventListener("submit", handleSubmit);
    
    const submitButton = document.querySelector(".login-button");
    if (submitButton) {
      submitButton.addEventListener("click", function(e) {
        e.preventDefault();
        handleSubmit(e);
      });
    }
  }
  
  const API_URL = "https://systemauth.alphadocere.cl/login.php";

  async function handleSubmit(event) {
    event.preventDefault();
    
    const username = document.querySelector("#username").value;
    const password = document.querySelector("#password").value;
    const mantenerSesion = document.querySelector("#mantenerSesion").checked;
    
    if (!username || !password) {
      alert("Por favor ingresa ambos campos: usuario y contraseña.");
      return;
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      controller.abort();
    }, 10000);

    try {
      const response = await fetch(API_URL, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: username, password: password }),
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        throw new Error(`Error HTTP: ${response.status}`);
      }

      const result = await response.json();

      if (result.success === true) {
        // Almacenar en localStorage/sessionStorage
        if (mantenerSesion) {
          localStorage.setItem("userLoggedIn", "true");
          localStorage.setItem("username", username);
          localStorage.setItem("sessionPermanent", "true");
          if (result.token) localStorage.setItem("token", result.token);
        } else {
          sessionStorage.setItem("userLoggedIn", "true");
          sessionStorage.setItem("username", username);
          localStorage.setItem("userLoggedIn", "true");
          if (result.token) {
            sessionStorage.setItem("token", result.token);
            localStorage.setItem("token", result.token);
          }
        }
        
        // Establecer cookies para compatibilidad con dashboard.html
        const expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + (mantenerSesion ? 30 : 1));
        
        document.cookie = `authToken=${result.token}; path=/; expires=${expiryDate.toUTCString()}`;
        document.cookie = `userName=${encodeURIComponent(username)}; path=/; expires=${expiryDate.toUTCString()}`;
        document.cookie = `userEmail=${encodeURIComponent(username)}; path=/; expires=${expiryDate.toUTCString()}`;
        
        // Redirigir al dashboard
        window.location.href = "../views/dashboard.html"; 
      } else {
        alert(result.error || "Usuario o contraseña incorrectos.");
      }
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        alert("La solicitud tardó demasiado tiempo. Verifica que el servidor esté funcionando correctamente.");
      } else {
        alert("Hubo un error al procesar tu solicitud: " + error.message);
      }
    }
  }
});
