async function cargarCategorias() {
    try {
        const response = await fetch(`${window.API_URL_PHP}controller/get_categorias.php`);
        const data = await response.json();
        
        if (data.exito) {
            const selectCategoria = document.getElementById('id_categoria');
            
            data.categorias.forEach(categoria => {
                const option = document.createElement('option');
                option.value = categoria.id_categoria;
                option.textContent = categoria.nombre_categoria;
                selectCategoria.appendChild(option);
            });
        } else {
            mostrarMensaje('Error al cargar categorías: ' + data.error, false);
        }
    } catch (error) {
        mostrarMensaje('Error de conexión al cargar categorías', false);
        console.error('Error:', error);
    }
}
