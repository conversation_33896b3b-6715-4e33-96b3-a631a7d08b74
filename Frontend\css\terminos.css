:root {
  --primary: #ff6b35;
  --primary-dark: #ff5722;
}

* {
  margin: 0;
  box-sizing: border-box;
  font-family: Arial, Helvetica, sans-serif;
  text-align: center;
}

nav ul li {
  float: left;
  margin: 10px;
  list-style: none;
}

footer {
  background-color: black;
  color: white;
  padding: 20px 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.footer-container {
  display: flex;
  justify-content: space-between;
  width: 100%;
  max-width: 1200px;
  margin: 0 ;
}

.titulo {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 50px;
  text-align: center;
}

.fecha {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  margin-bottom: 20px;
}

h1 {
  font-family: 'Krona One', sans-serif;
  color: white;
  max-width: 900px;
  padding:2rem;
}

h3 {
  font-family: 'Krona One', sans-serif;
  color: white;
  max-width: 900px;
  text-align: justify;
  padding-left: 100px;
  padding-right: 100px;
  padding-bottom: 10px;
  padding-top: 5px;
}

h4 {
  font-family: 'Krona One', sans-serif;
  color: white;
  max-width: 900px;
  text-align: justify;
  padding-left: 100px;
  padding-right: 100px;
  padding-bottom: 10px;
  padding-top: 5px;
}

div p {
  padding-left: 100px;
  padding-right: 100px;
  padding-bottom: 10px;
  text-align: justify;
}

.container {
  max-width: 900px;
  padding: 0 15px;
  margin: auto;
}

/* Fondo negro para toda la página */
html, body {
  margin: 0;
  padding: 0;
  background-color: #111;
  font-family: 'Space Grotesk', sans-serif;
  color: white;
}

.navbar {
  position: sticky;
  top: 0;
  width: 100%;
  background-color: #111;
  border-bottom: 3px solid var(--primary); /* Línea separadora naranja */
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0,0,0,0.7);
  font-family: 'Space Grotesk', sans-serif;
}

.navbar-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.navbar-logo {
  color: var(--primary);
  font-weight: 700;
  font-size: 1.8rem;
  cursor: pointer;
  user-select: none;
}

.navbar-menu {
  list-style: none;
  display: flex;
  gap: 2rem;
}

.navbar-menu li a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.navbar-menu li a:hover,
.navbar-menu li a:focus {
  color: var(--primary);
}

/* Menú hamburguesa */
.navbar-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 0.5rem;
}

.navbar-toggle span {
  width: 25px;
  height: 3px;
  background-color: var(--primary);
  margin: 3px 0;
  transition: 0.3s;
  border-radius: 2px;
}

/* Animación del icono hamburguesa */
.navbar-toggle.active span:nth-child(1) {
  transform: rotate(-45deg) translate(-5px, 6px);
}

.navbar-toggle.active span:nth-child(2) {
  opacity: 0;
}

.navbar-toggle.active span:nth-child(3) {
  transform: rotate(45deg) translate(-5px, -6px);
}

/* Responsive */
@media (max-width: 768px) {
  .navbar-toggle {
    display: flex;
  }

  .navbar-menu {
    position: fixed;
    top: 70px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 70px);
    background-color: #111;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-top: 2rem;
    transition: left 0.3s ease;
    gap: 1.5rem;
  }

  .navbar-menu.active {
    left: 0;
  }

  .navbar-menu li {
    width: 100%;
    text-align: center;
  }

  .navbar-menu li a {
    display: block;
    padding: 1rem;
    font-size: 1.2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
}

section {
  display: flex;
  align-items: center;
  gap: 2rem;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  background-color: #ffffff;
}

.section {
  padding: 80px 0;
  min-height: 100vh;
  display: flex;
  justify-content: center;
}

.footer {
  background-color: #111;
  padding: 4rem 2rem 2.5rem;
  color: rgba(255, 255, 255, 0.8);
  font-family: 'Space Grotesk', sans-serif;
  border-top: 3px solid var(--primary); /* Línea separadora naranja */
  margin-top: 4rem;
}

.footer-grid {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  max-width: 100vw;
  width: 100vw;
  margin: 0;
  flex-wrap: wrap;
  gap: 1.5rem;
  padding-left: 0;
  padding-right: 0;
}

.footer-left {
  text-align: left;
  flex: 1 1 300px;
}

.footer-right {
  text-align: right;
  flex: 1 1 300px;
}

.footer-left h2 {
  color: var(--primary);
  font-size: 1.8rem;
  margin-bottom: 0.3rem;
}

.footer-left p {
  font-size: 0.9rem;
  color: rgba(255,255,255,0.6);
}

.footer-right p {
  margin: 0.2rem 0;
  font-size: 0.95rem;
}

.footer-right a {
  color: var(--primary);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-right a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

@media (max-width: 600px) {
    .footer-grid {
      flex-direction: column;
      text-align: center;
      gap: 1rem;
    }

    .footer-right p {
      margin: 0.3rem 0;
    }
}
