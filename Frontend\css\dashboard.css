:root {
            --primary: #ff6b35;
            --primary-dark: #ff5722;
            --secondary: #222;
            --background: #0a0a0a;
            --text: #fff;
            --card-bg: #181818;
            --card-shadow: 0 5px 20px rgba(255, 107, 53, 0.08);
        }
        
        body {
            font-family: 'Space Grotesk', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: var(--background);
            color: var(--text);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        header {
            background: var(--secondary);
            color: var(--primary);
            padding: 1.2rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 3px solid var(--primary);
            box-shadow: 0 2px 8px rgba(0,0,0,0.4);
        }
        
        .logo {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary);
            text-decoration: none;
            letter-spacing: 1px;
        }
        
        .user-menu {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .user-info {
            margin-right: 18px;
            text-align: right;
        }
        
        .user-name {
            font-weight: bold;
            font-size: 1.1rem;
            color: var(--primary);
        }
        
        .user-email {
            font-size: 0.95rem;
            color: #fff;
            opacity: 0.7;
        }
        
        .logout-btn {
            background: var(--primary);
            color: #fff;
            border: none;
            padding: 8px 18px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: background 0.3s;
        }
        .logout-btn:hover {
            background: var(--primary-dark);
        }
        
        .container {
            max-width: 600px;
            margin: 40px auto;
            padding: 0 20px;
            flex-grow: 1;
        }
        
        .welcome-card {
            background: var(--card-bg);
            border-radius: 18px;
            box-shadow: var(--card-shadow);
            padding: 36px 28px;
            margin-bottom: 30px;
            text-align: center;
            border: 2px solid var(--primary);
        }
        
        .welcome-card h1 {
            color: var(--primary);
            margin-bottom: 18px;
            font-size: 2.2rem;
            font-weight: 700;
        }
        
        .welcome-card p {
            color: #fff;
            font-size: 1.15rem;
            margin: 1.2rem 0;
        }
        
        .welcome-card strong {
            color: var(--primary);
            font-weight: 600;
        }
        
        #visitas-landing, #fecha-landing {
            color: var(--primary);
            font-size: 1.3rem;
            font-weight: 700;
            margin-left: 8px;
        }
        
        footer {
            background: var(--secondary);
            color: #fff;
            text-align: center;
            padding: 20px;
            margin-top: auto;
            border-top: 3px solid var(--primary);
            font-size: 1rem;
            letter-spacing: 0.5px;
        }

        .btn-ir-contactos {
            display: inline-block;
            margin-top: 18px;
            background: var(--primary);
            color: #fff;
            padding: 12px 28px;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 700;
            text-decoration: none;
            transition: background 0.2s;
            box-shadow: 0 2px 8px rgba(255,107,53,0.10);
        }
        .btn-ir-contactos:hover {
            background: var(--primary-dark);
        }

        .btn-ir-contactos-header {
            background: var(--primary);
            color: #fff;
            padding: 8px 18px;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 600;
            text-decoration: none;
            margin-left: 16px;
            margin-right: 10px;
            transition: background 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
            border: none;
            cursor: pointer;
        }
        .btn-ir-contactos-header:hover {
            background: var(--primary-dark);
        }