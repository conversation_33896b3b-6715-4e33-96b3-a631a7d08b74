<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>Contactos</title>
    <link rel="stylesheet" href="../css/contactos.css">
</head>
<body>
    <h1>Contactos</h1>
    <a class="volver-btn"  href="dashboard.html">Volver</a>
    <div class="tabs">
        
  <div id="filter-tarjeta" style="margin: 1rem 0; display: none;">
  <label for="filtro-estado"><strong>Filtrar por estado:</strong></label>
  <select id="filtro-estado">
    <option value="todos">Todos</option>
    <option value="Contacto Inicial">Contacto Inicial</option>
    <option value="Match Inicial">Match Inicial</option>
    <option value="Match Confirmado">Match Confirmado</option>
    <option value="Match Rechazado">Match Rechazado</option>
    <option value="Match Inactivo">Match Inactivo</option>
    <option value="Match Finalizado">Match Finalizado</option>
    <option value="">Sin estado</option>
  </select>
</div>


        
        
        <button id="tab-tarjetas" class="tab-btn active">Tarjetas</button>
        
        <button id="tab-admin-tabla" class="tab-btn">Admin Tabla</button>

        <button id="tab-estado-perfiles" class="tab-btn">Perfiles</button>
    </div>
    <div id="tarjetas-container"></div>
    <div id="tabla-container" style="display:none;"></div>
    <div id="admin-tabla-container" style="display:none;"></div>
    <div id="estado-perfiles-container" style="display:none;"></div>
    <div id="confirm-modal" class="modal" style="display:none;">
      <div class="modal-content">
        <p>¿Estás seguro de que deseas realizar esta acción?</p>
        <button id="confirm-btn" class="btn-confirmar">Confirmar</button>
        <button id="cancel-btn" class="btn-cancelar">Cancelar</button>
      </div>
    </div>

    <!-- Modal de detalles de respuestas -->
    <div id="detalles-modal" class="modal" style="display:none;">
      <div id="detalles-modal-content" class="modal-content detalles-modal-content">
        <!-- El contenido se llenará dinámicamente -->
      </div>
    </div>

<script src="../js/contactos.js"></script>
<script src="../js/tarjetas.js"></script>
<script src="../js/adminTabla.js"></script>
<script src="../js/perfiles.js"></script>
<script src="../js/verificarlogin.js"></script>
<script src="../js/config.js"></script>
</body>
</html>
