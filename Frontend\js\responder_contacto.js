document.addEventListener('DOMContentLoaded', function() {
    // Obtener el token de la URL
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');
    
    if (!token) {
        mostrarError("No se proporcionó un token.");
        return;
    }
    
    // Cargar datos del contacto
    cargarDatosContacto(token);
    
    // Configurar eventos del formulario
    configurarEventosFormulario();
});

function cargarDatosContacto(token) {
    fetch(`../../Backend/controller/get_contacto_por_token.php?token=${encodeURIComponent(token)}`)
        .then(response => response.json())
        .then(data => {
            if (data.exito) {
                mostrarContacto(data.contacto, token);
            } else {
                mostrarError(data.error || "Error al cargar los datos del contacto.");
            }
        })
        .catch(error => {
            console.error('Error:', error);
            mostrarError("Error de conexión. Inténtalo de nuevo.");
        });
}

function mostrarContacto(contacto, token) {
    // Ocultar contenedor de error
    document.getElementById('error-container').style.display = 'none';
    
    // Mostrar contenedor de contacto
    const contactoContainer = document.getElementById('contacto-container');
    contactoContainer.style.display = 'block';
    
    // Llenar datos del contacto
    document.getElementById('nombre-contacto').textContent = `Has recibido una solicitud de: ${contacto.nombre}`;
    document.getElementById('email-contacto').textContent = contacto.email;
    document.getElementById('telefono-contacto').textContent = contacto.telefono;
    document.getElementById('descripcion-contacto').innerHTML = contacto.descripcion.replace(/\n/g, '<br>');
    
    // Formatear periodo
    const fechaInicio = new Date(contacto.fechainicio).toLocaleDateString('es-ES');
    const fechaTermino = new Date(contacto.fechatermino).toLocaleDateString('es-ES');
    document.getElementById('periodo-contacto').textContent = `${fechaInicio} a ${fechaTermino}`;
    
    // Establecer token en el formulario
    document.getElementById('token-hidden').value = token;
}

function mostrarError(mensaje) {
    // Ocultar contenedor de contacto
    document.getElementById('contacto-container').style.display = 'none';
    
    // Mostrar contenedor de error
    const errorContainer = document.getElementById('error-container');
    errorContainer.style.display = 'block';
    
    document.getElementById('error-message').textContent = mensaje;
    
    // Redirigir después de 5 segundos
    setTimeout(() => {
        window.location.href = '/index.html';
    }, 5000);
}

function configurarEventosFormulario() {
    // Evento para mostrar campos adicionales según el modo de contacto
    const modoContacto = document.getElementById('modo_contacto');
    modoContacto.addEventListener('change', mostrarCamposAdicionales);
    
    // Evento para el envío del formulario
    const formulario = document.getElementById('formulario-respuesta');
    formulario.addEventListener('submit', manejarEnvioFormulario);
}

function mostrarCamposAdicionales() {
    const modo = document.getElementById('modo_contacto').value;
    
    // Ocultar todos los grupos primero
    document.getElementById('grupo_direccion').style.display = 'none';
    document.getElementById('grupo_link').style.display = 'none';
    document.getElementById('grupo_apoyo').style.display = 'none';
    
    // Mostrar el grupo correspondiente
    switch(modo) {
        case 'presencial':
            document.getElementById('grupo_direccion').style.display = 'block';
            break;
        case 'videollamada':
            document.getElementById('grupo_link').style.display = 'block';
            break;
        case 'agente':
            document.getElementById('grupo_apoyo').style.display = 'block';
            break;
    }
}

function manejarEnvioFormulario(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const submitButton = event.target.querySelector('button[type="submit"]');
    
    // Deshabilitar botón para evitar envíos múltiples
    submitButton.disabled = true;
    submitButton.textContent = 'Enviando...';
    
    fetch(event.target.action, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        // Verificar si la respuesta es JSON
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            return response.json();
        } else {
            // Si no es JSON, leer como texto para debug
            return response.text().then(text => {
                console.error('Respuesta no JSON recibida:', text);
                throw new Error('El servidor no devolvió JSON válido');
            });
        }
    })
    .then(data => {
        if (data.exito) {
            // Redirigir a página de confirmación
            window.location.href = 'respuesta_confirmada_creativo.html';
        } else {
            alert('Error: ' + (data.error || 'Error desconocido'));
            submitButton.disabled = false;
            submitButton.textContent = 'Enviar Respuesta';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error de conexión. Inténtalo de nuevo.');
        submitButton.disabled = false;
        submitButton.textContent = 'Enviar Respuesta';
    });
}

// Función para validar fechas
function validarFechas() {
    const fecha1 = document.querySelector('input[name="fecha1"]').value;
    const fecha2 = document.querySelector('input[name="fecha2"]').value;
    const fecha3 = document.querySelector('input[name="fecha3"]').value;
    
    if (fecha2 && new Date(fecha2) <= new Date(fecha1)) {
        alert('La fecha 2 debe ser posterior a la fecha 1');
        return false;
    }
    
    if (fecha3 && new Date(fecha3) <= new Date(fecha2 || fecha1)) {
        alert('La fecha 3 debe ser posterior a las fechas anteriores');
        return false;
    }
    
    return true;
}

// Agregar validación de fechas al formulario
document.addEventListener('DOMContentLoaded', function() {
    const formulario = document.getElementById('formulario-respuesta');
    if (formulario) {
        formulario.addEventListener('submit', function(event) {
            if (!validarFechas()) {
                event.preventDefault();
            }
        });
    }
}); 