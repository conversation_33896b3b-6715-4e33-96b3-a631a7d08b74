<?php
// enviar.php

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// Cargar Composer Autoload para PHPMailer y Dotenv
require 'vendor/autoload.php';

// Cargar variables de entorno
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Conexión a base de datos (ajusta estos datos si usas otro host/usuario)
include '../models/conexion.php';

// Recibir datos del formulario
$nombre         = $_POST['nombre'] ?? '';
$telefono       = $_POST['telefono'] ?? '';
$email          = $_POST['email'] ?? '';
$descripcion    = $_POST['descripcion'] ?? '';
$fechainicio    = $_POST['fechainicio'] ?? '';
$fechatermino   = $_POST['fechatermino'] ?? '';
$id_perfil      = $_POST['id_perfil_creativo'] ?? 0;

// Validar ID perfil
if (!$id_perfil || !is_numeric($id_perfil)) {
    die("ID de perfil inválido");
}

// Obtener email del perfil creativo
$stmt = $conexion->prepare("SELECT email FROM perfiles_creativos WHERE id = ?");
$stmt->bind_param("i", $id_perfil);
$stmt->execute();
$resultado = $stmt->get_result();

if ($resultado->num_rows === 0) {
    die("No se encontró el perfil creativo.");
}

$perfil = $resultado->fetch_assoc();
$correo_destino = $perfil['email'];



// Enviar correo con PHPMailer
$mail = new PHPMailer(true);

try {
    // Configuración SMTP
    $mail->isSMTP();
    $mail->Host       = $_ENV['MAIL_HOST'];
    $mail->SMTPAuth   = true;
    $mail->Username   = $_ENV['MAIL_USERNAME'];
    $mail->Password   = $_ENV['MAIL_PASSWORD'];
    $mail->SMTPSecure = 'tls';
    $mail->Port       = $_ENV['MAIL_PORT'];

    // Remitente y destinatario
    $mail->setFrom($_ENV['MAIL_FROM'], $_ENV['MAIL_FROM_NAME']);
    $mail->addAddress($correo_destino);

    // Contenido
    $mail->isHTML(true);
    $mail->Subject = "Nuevo Match de  $nombre";
    $mail->Body    = "
        <h2>Nuevo mensaje recibido</h2>
        <p><strong>Nombre:</strong> $nombre</p>
        <p><strong>Email:</strong> $email</p>
        <p><strong>WhatsApp:</strong> $telefono</p>
        <p><strong>Descripción:</strong> $descripcion</p>
        <p><strong>Fecha inicio:</strong> $fechainicio</p>
        <p><strong>Fecha término:</strong> $fechatermino</p>
    ";

    $mail->send();
    echo "Mensaje enviado correctamente al perfil creativo.";
} catch (Exception $e) {
    echo "Error al enviar el mensaje: {$mail->ErrorInfo}";
}

$conexion->close();
