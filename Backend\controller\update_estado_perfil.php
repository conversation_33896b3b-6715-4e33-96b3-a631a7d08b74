<?php
header('Content-Type: application/json');
require_once __DIR__ . '/../models/conexion.php';

$data = json_decode(file_get_contents('php://input'), true);
$id = $data['id'] ?? null;
$estado_perfil = $data['estado_perfil'] ?? null;

if ($id !== null && $estado_perfil !== null) {
    try {
        $stmt = $conn->prepare('UPDATE perfil_creativo SET estado_perfil = ? WHERE id_pcreativa = ?');
        $stmt->execute([$estado_perfil, $id]);
        echo json_encode(['exito' => true]);
    } catch (PDOException $e) {
        echo json_encode(['exito' => false, 'error' => $e->getMessage()]);
    }
} else {
    echo json_encode(['exito' => false, 'error' => 'Datos incompletos']);
}