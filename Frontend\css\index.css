* {
    margin: 0;
    
    box-sizing: border-box;
    font-family: Arial, Helvetica, sans-serif;
    text-align: center;
}

nav ul li {
float: left;
margin: 10px;
list-style: none;
}

.clearfix {
    clear: both;
}  

.contenedor_cards {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    justify-content: center;
}

.tarjeta {
    border-radius: 10px;
    padding: 15px;
    width: 300px;
    text-align: center;
}

.tarjeta img {
    width: 100%;
    border-radius: 2px;
}

button {
    color: black;
    border: none;
    padding: 10px;
    border-radius: 5px;
}

footer {
    background-color: black;
    color: white;
    padding: 20px 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.footer-container {
    display: flex;
    justify-content: space-between;
    width: 100%;
    max-width: 1200px;
    margin: 0 ;
}

.footer-section,
.footer-section2 {
    display: flex;
    flex-direction: column;
}

.footer-title {
    font-size: 1.5em;
    font-weight: bold;
}

.footer-links,
.footer-section2 {
    margin-top: 10px;
}

.footer-txt, .footer-txt2 {
    color: white;
    text-decoration: none;
    font-size: 1em;
    margin-bottom: 5px;
}

.footer-txt:hover, .footer-txt2:hover {
    text-decoration: underline;
}

.icons_redes {
    display: flex;
    gap: 10px;
}

.icons_redes img {
    width: 30px;
    height: 30px;
    transition: transform 0.3s ease;
}

.icons_redes img:hover {
    transform: scale(1.2);
}
.footer-links {
    display: flex;
    flex-direction: column;
    gap: 5px; 
}

.container_titulo {
    background-image: url('/Assets/Imagenes/orange-texture.jpg');
    background-size: cover;
    background-position: center;
    height: 90vh;
    display: flex;  
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    position: relative;
   
  
}

h1, .P-1 {
    font-family: 'Krona One', sans-serif;
    color: white;
    max-width: 900px;
    padding:2rem;
}

.P-1 {
    font-size: 1.43rem;
}

.button-register {
    border: 2px solid white;
    color: white;
    background: transparent;
    padding: 10px 40px;
    border-radius: 25px;
    margin-top: 6rem;
}

.button-register:hover {
    background-color: white;
    color: #FF8A00;
    cursor: pointer;
}

/* Segundo componente de la pagina  */
section {
    display: flex;
    align-items: center;
    gap: 2rem;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    background-color: #ffffff;
}

.image-section {
    flex: 1;
    position: relative;
}

.image-section img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.image-section p {
    margin-top: 1rem;
    font-size: 0.9rem;
    color: #666;
}

.section-info {
    flex: 1;
    padding: 1rem;
}

.section-info h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #333;
}

.section-info p {
    margin-bottom: 1.5rem;
    color: #666;
}

.section-info a {
    border: 2px solid rgb(0, 0, 0);
    color: rgb(0, 0, 0);
    background: transparent;
    padding: 10px 25px;
    border-radius: 25px;
    text-decoration: none;
    
}

.section-info a:hover {
    background-color: #ff7514;
}

section h2 {
    text-align: center;
    font-family: 'Krona One', sans-serif;
    font-weight: 700;
    margin-bottom: 2rem;
    color: #333;
}

.image-section h2 {
    font-size: 2rem;
    max-width: 800px;
    margin: 0 auto 2rem auto;
    line-height: 1.4;
}

.titulo-central {
    text-align: center;
}
.titulo-central {
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    max-width: 60%; 
    color: #434343;
    font-family: 'Montserrat', sans-serif;
}

/* Seccion de registro  */
.opciones-container {
    width: 100%;
    padding: 40px 20px;
}

.opciones-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    gap: 40px;
    justify-content: space-between;
}

.opcion-creativo, .opcion-proyecto {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.titulo-creativo, .titulo-proyecto {
    font-family: 'Krona One', sans-serif;
    font-size: 2.5rem;
    color: #FF8A00;
    margin-bottom: 20px;
    width: 100%;
    order: 1;
}

.descripcion-creativo, .descripcion-proyecto {
    font-family: 'Montserrat', sans-serif;
    font-size: 1.2rem;
    line-height: 1.6;
    color: #333;
    margin-bottom: 30px;
    width: 100%;
    order: 2;
}

.btn-registro, .btn-explorar {
    width: 300px;
    padding: 15px 30px;
    border: 2px solid #000;
    border-radius: 25px;
    text-decoration: none;
    color: #000;
    font-family: 'Montserrat', sans-serif;
    font-weight: bold;
    text-align: center;
    background: transparent;
    order: 3;
    margin-top: auto;
    -webkit-border-radius: 25px;
    -moz-border-radius: 25px;
    -ms-border-radius: 25px;
    -o-border-radius: 25px;
}

.btn-registro:hover, .btn-explorar:hover {
    background-color: #ff7514;
    color: #fff;
}

/* barra de selecion de eleciones */
.menu-bar {
    background-color: white;
    padding: 15px 0;
    border-bottom: 1px solid #e5e5e5;
    width: 100%;
}

.menu-container {
    max-width: 1200px;
    margin: 0 auto;
}

.menu-list {
    list-style: none;
    display: flex;
    gap: 30px;
    justify-content: center;
}

.menu-item a {
    text-decoration: none;
    color: #666;
    font-family: Arial, sans-serif;
    font-size: 20px;
    transition: color 0.3s ease;
}

.menu-item a:hover {
    color: #000;
}

.menu-item.active a {
    color: #000;
}



.container {
    max-width: 900px;
    padding: 0 15px;
    margin: auto;
}


.section {
    padding: 80px 0;
    min-height: 100vh;
    display: flex;
    justify-content: center;
}


.section-cards {
    display: flex;
    justify-content: space-between;
    align-items: stretch;
}


.section-card {
    background-color: #ffffff;
    border-radius: 12px;
    padding: 30px;
    position: relative;
    gap: 30px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease-in-out, background-color 0.3s ease-in-out;
    flex-basis: 31%;
}

.section-card:hover {
    transform: translateY(-10px);
    background-image: linear-gradient(45deg, #FFE8D6, #FDCDB0);
    cursor: pointer;
}

/* Títulos de las tarjetas */
.section-card h2 {
    font-size: 24px;
    color: #333333;
   
}


.listado {
    list-style: none;
    padding: 0;
    color: #666666;
    line-height: 1.5;
    margin-bottom: 20px;
}

/* Botón dentro de las tarjetas */
.section-card a {
    display: inline-block;
    text-transform: capitalize;
    background-color:#ff8c42;
    color: #ffffff;
    padding: 8px 25px;
    border-radius: 25px;
    font-weight: 500;
    text-decoration: none;
    transition: background-color 0.3s ease-in-out;
}

.section-card a:hover {
    background-color: #e67332;
}

.title-service {
    text-align: center;
    font-family: 'Krona One', sans-serif;
    font-size: 2.5rem;
    color: #FF8A00;
    margin: 1rem auto;
    max-width: 800px;
    padding: 0 20px;
    position: relative;
    display: block;
}

.section {
    padding: 2rem;
    background-color: #ffffff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

.section-cards-general {
    display: flex;
    gap: 3rem;
    justify-content: center;
    flex-wrap: wrap;
}

.section-card {
    background: rgb(255, 255, 255);
    border-radius: 10px;
    padding: 2rem;
  
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    flex: 1;
    min-width: 300px;
    max-width: 350px;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;
}

.section-card:hover {
    transform: translateY(-5px);
}

.section-card h2 {
    font-family: 'Krona One', sans-serif;
    color: #FF8A00;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    text-align: center;
}

.listado {
    list-style: none;
    padding: 0;
    margin: 0;
    flex-grow: 1;
}

.listado li {
    font-family: 'Montserrat', sans-serif;
    margin-bottom: 1rem;
    padding-left: 1.5rem;
    position: relative;
    line-height: 1.4;
}

.section-card a {
    display: inline-block;
    width: 100%;
    padding: 1rem;
    background-color: #FF8A00;
    color: white;
    text-decoration: none;
    text-align: center;
    border-radius: 5px;
    margin-top: 2rem;
    font-family: 'Montserrat', sans-serif;
    font-weight: bold;
    transition: background-color 0.3s ease;
}

.section-card a:hover {
    background-color: #e67e00;
}


/*aca empieza el navbar*/

.navbar {
    background-color: white;
    padding: 0.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 9999;
}

.nav-brand {
    display: flex;
    align-items: center;
    
}

.nav-logo {
    width: 50px;
    height: 50px;
}

.brand-name {
    font-family: 'Krona One', sans-serif;
    font-size: 1.2rem;
    color: #333;
}

.nav-menu {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.nav-link:hover {
    color: #c78100;
}

/*aca termina el navbar*/

/*aca empieza las tarjetas de los  proyectos*/


.tarjeta {
    width: 280px;
    margin: 15px;
}

.tarjeta img {
    width: 100%;
    border-radius: 4px;
}

.tarjeta h3 {
    font-size: 14px;
    margin: 8px 0;
}

.tarjeta h4 {
    text-align: left;
    margin: 2px 0;
    font-size: 12px;
}



.stats-item {
    display: flex;
    align-items: center;
    margin: 1.5rem 0rem;
    font-size: 14px;
}

.stats-item h4 {
    margin: 0;
    margin-right: auto;
}

.likes {
    margin-left: 12px;
}

.ver-mas {
    width: 50%;
    padding: 6px;
    border: 1px solid #000000;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    margin-top: 1rem;
    
}
.ver-mas:hover {
    background-color: #ff7514;
    color: #fff;
}

.contenedor_cards {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}
/*aca termina las tarjetas de los  proyectos*/
