
    body {
      background: linear-gradient(135deg, #ff8a00 0%, #000000 100%);
      font-family: 'Arial', sans-serif;
      height: 100vh;
      margin: 0;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    
    .login-container {
      background-color: rgba(255, 255, 255, 0.9);
      border-radius: 10px;
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
      padding: 30px;
      width: 90%;
      max-width: 400px;
    }
    
    h1 {
      color: #ff6b00;
      text-align: center;
      margin-bottom: 30px;
    }
    
    .form-group {
      margin-bottom: 20px;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 5px;
      color: #333;
      font-weight: bold;
    }
    
    .form-group input {
      width: 100%;
      padding: 12px;
      border: 1px solid #ddd;
      border-radius: 5px;
      font-size: 16px;
      transition: border-color 0.3s;
    }
    
    .form-group input:focus {
      border-color: #ff8a00;
      outline: none;
      box-shadow: 0 0 5px rgba(255, 138, 0, 0.5);
    }
    
    .login-button {
      background: linear-gradient(to right, #ff8a00, #ff5722);
      color: white;
      border: none;
      border-radius: 5px;
      padding: 12px;
      width: 100%;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      transition: transform 0.3s, box-shadow 0.3s;
    }
    
    .login-button:hover {
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(255, 138, 0, 0.4);
    }
    
    .form-footer {
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
      font-size: 14px;
    }
    
    .remember-me {
      display: flex;
      align-items: center;
    }
    
    .remember-me input {
      margin-right: 5px;
    }
    
    .forgot-password {
      color: #ff8a00;
      text-decoration: none;
    }
    
    .forgot-password:hover {
      text-decoration: underline;
    }
    
    .back-to-top {
      position: fixed;
      bottom: 20px;
      left: 20px;
    }
    
    .arrow {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 40px;
      height: 40px;
      background-color: #ff8a00;
      color: white;
      border-radius: 50%;
      text-decoration: none;
      font-size: 20px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
      transition: background-color 0.3s;
    }
    
    .arrow:hover {
      background-color: #ff5722;
    }
