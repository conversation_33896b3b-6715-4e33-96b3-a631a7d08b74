<?php
header("Access-Control-Allow-Origin: *");
header('Content-Type: application/json');
require_once __DIR__ . '/../models/conexion.php';

// Si se proporciona un ID específico, obtener ese proyecto con todas sus imágenes
if (isset($_GET['id'])) {
    $id_proyecto = $_GET['id'];

    // Obtener información del proyecto
    $stmt = $conn->prepare("
        SELECT
            p.id_proyectos,
            p.titulo_proyecto,
            p.descripcion,
            p.id_pcreativa
        FROM
            proyectos p
        WHERE p.id_proyectos = ?
    ");
    $stmt->execute([$id_proyecto]);
    $proyecto = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$proyecto) {
        echo json_encode(['exito' => false, 'mensaje' => 'Proyecto no encontrado']);
        exit;
    }

    // Obtener todas las imágenes del proyecto
    $stmt_imagenes = $conn->prepare("
        SELECT
            id_imagen,
            TO_BASE64(imagen) AS imagen_base64
        FROM
            proyecto_imagenes
        WHERE id_proyecto = ?
        ORDER BY id_imagen
    ");
    $stmt_imagenes->execute([$id_proyecto]);
    $imagenes = $stmt_imagenes->fetchAll(PDO::FETCH_ASSOC);

    $proyecto['imagenes'] = $imagenes;

    echo json_encode(['exito' => true, 'proyecto' => $proyecto]);
} else {
    // Obtener todos los proyectos (comportamiento original)
    $stmt = $conn->prepare("
        SELECT
            p.id_proyectos,
            p.titulo_proyecto,
            p.descripcion,
            (
                SELECT TO_BASE64(imagen)
                FROM proyecto_imagenes
                WHERE id_proyecto = p.id_proyectos
                LIMIT 1
            ) AS imagen_base64
        FROM
            proyectos p
        ORDER BY p.id_proyectos DESC
    ");
    $stmt->execute();
    $proyectos = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode(['exito' => true, 'proyectos' => $proyectos]);
}
