function mostrarTarjetas(contactos, estadoFiltro = 'todos') {
  const tarjetasContainer = document.getElementById('tarjetas-container');
  tarjetasContainer.innerHTML = '';

  const filtrados = estadoFiltro === 'todos'
    ? contactos
    : contactos.filter(c => (c.estado || '') === estadoFiltro);

  filtrados.forEach((c) => {
    const card = document.createElement('div');
    card.className = 'contacto-card';
    const estado = c.estado || 'Sin estado';
    const estadoClass = estado.toLowerCase().replace(/\s+/g, '-');

    card.innerHTML = `
      <div class="estado-badge ${estadoClass}">${estado}</div>
      <div class="card-line"><span class="label">📛 Nombre:</span><span>${c.nombre}</span></div>
      <div class="card-line"><span class="label">📞 Teléfono:</span><span>${c.telefono || '-'}</span></div>
      <div class="card-line"><span class="label">✉️ Email:</span><span>${c.email || '-'}</span></div>
      <div class="card-line"><span class="label">📝 Descripción:</span><span>${c.descripcion || '-'}</span></div>
      <div class="card-line"><span class="label">📅 Desde:</span><span>${c.fechainicio || '-'}</span></div>
      <div class="card-line"><span class="label">📅 Hasta:</span><span>${c.fechatermino || '-'}</span></div>
      <div class="card-line"><span class="label">🎨 Creativo:</span><span>${(c.nombre_creativo || '-') + (c.apellido_creativo ? ' ' + c.apellido_creativo : '')}</span></div>
      <div class="card-actions">
        <button class="ver-mas-btn" onclick="verDetallesContacto(${c.id})">Ver más</button>
      </div>`;
    tarjetasContainer.appendChild(card);
  });
}

window.verDetallesContacto = function (idContacto) {
  fetch(`${urlRespuestas}?id_contacto=${idContacto}`)
    .then(res => res.json())
    .then(respuestas => mostrarModalDetalles(idContacto, respuestas))
    .catch(() => alert('Error al cargar los detalles'));
};

function mostrarModalDetalles(id, respuestas) {
  let html = `
    <div class="modal-header">
      <h2>Detalles del match</h2>
      <span class="close" onclick="cerrarModalDetalles()">&times;</span>
    </div>`;

  if (respuestas.length > 0) {
    respuestas.forEach(r => {
      html += `
        <div class="respuesta-item">
          <h3>Respuesta del creativo</h3>
          <p><strong>Fecha Opción 1:</strong> ${r.fecha_opcion_1 || 'No especificada'}</p>
          <p><strong>Fecha Opción 2:</strong> ${r.fecha_opcion_2 || 'No especificada'}</p>
          <p><strong>Fecha Opción 3:</strong> ${r.fecha_opcion_3 || 'No especificada'}</p>
          <p><strong>Modo de Contacto:</strong> ${r.modo_contacto}</p>
          ${r.modo_contacto === 'presencial' ? `<p><strong>Dirección:</strong> ${r.detalle_contacto}</p>` : ''}
          ${r.modo_contacto === 'videollamada' ? `<p><strong>Link:</strong> <a href="${r.link_videollamada}" target="_blank">${r.link_videollamada}</a></p>` : ''}
          ${r.modo_contacto === 'agente' ? `<p><strong>Tipo Apoyo:</strong> ${r.tipo_apoyo}</p>` : ''}
          <p><strong>Creado:</strong> ${r.creado_en}</p>
        </div>`;
    });
  } else {
    html += '<p>No hay respuestas registradas para este contacto.</p>';
  }

  modalDetallesContent.innerHTML = html;
  modalDetalles.style.display = 'flex';
}

window.cerrarModalDetalles = function () {
  modalDetalles.style.display = 'none';
};
