<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: application/json'); 
require_once __DIR__ . '/conexion.php';

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}


$data = json_decode(file_get_contents("php://input"), true);

try {
    // Verificar si el correo ya existe
    $check = $conn->prepare("SELECT COUNT(*) FROM perfil_creativo WHERE correo = ?");
    $check->execute([$data['correo']]);
    
    if ($check->fetchColumn() > 0) {
        http_response_code(400);
        echo json_encode([
            'exito' => false,
            'error' => 'El correo electrónico ya está registrado'
        ]);
        exit();
    }
    
    $stmt = $conn->prepare("INSERT INTO perfil_creativo (id_pcreativa, nombre, apellido, titulo_profesional, telefono, id_categoria, descripcion, experiencia, foto_perfil, startdate, enddate, correo) VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $foto_perfil = $data['foto_perfil'];
    if (is_string($foto_perfil) && strpos($foto_perfil, 'data:image') === 0) {
        $foto_perfil = explode(',', $foto_perfil)[1];
        $foto_perfil = base64_decode($foto_perfil);
    } else if (empty($foto_perfil)) {
        $foto_perfil = file_get_contents(__DIR__ . '/../../Frontend/assets/default-profile.jpg');
        if (!$foto_perfil) {
            $foto_perfil = null;
        }
    }
    
    // Formatear fechas o usar NULL si no están presentes
    $startdate = !empty($data['startdate']) ? $data['startdate'] : date('Y-m-d');
    $enddate = !empty($data['enddate']) ? $data['enddate'] : null;
    
    $stmt->execute([
        $data['id_pcreativa'], 
        $data['nombre'], 
        $data['apellido'],
        $data['titulo_profesional'], 
        $data['telefono'],
        $data['id_categoria'], 
        $data['descripcion'],
        $data['experiencia'], 
        $foto_perfil,
        $startdate,
        $enddate,
        $data['correo']
    ]);
    
    $perfil_creativoid = $conn->lastInsertId();
    
    echo json_encode([
        'exito' => true,
        'mensaje' => 'Perfil creativo creado correctamente',
        'id' => $perfil_creativoid
    ]);
    
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'exito' => false,
        'error' => 'Error al crear el perfil creativo: ' . $e->getMessage()
    ]);
}
