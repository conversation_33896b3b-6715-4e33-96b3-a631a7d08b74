<?php
require_once '../models/conexion.php';

$titulo = $_POST['titulo_proyecto'] ?? '';
$descripcion = $_POST['descripcion'] ?? '';
$id_pcreativa = $_POST['id_pcreativa'] ?? '';

if (!$titulo || !$descripcion || !$id_pcreativa) {
    echo "Faltan datos obligatorios";
    exit;
}

try {
    
    $stmt = $conn->prepare("INSERT INTO proyectos (titulo_proyecto, descripcion, id_pcreativa) VALUES (?, ?, ?)");
    $stmt->execute([$titulo, $descripcion, $id_pcreativa]);
    $id_proyecto = $conn->lastInsertId();

    // Guardar imágenes
    if (!empty($_FILES['imagenes']['name'][0])) {
        foreach ($_FILES['imagenes']['tmp_name'] as $key => $tmp_name) {
            if ($_FILES['imagenes']['error'][$key] === UPLOAD_ERR_OK) {
                $imgData = file_get_contents($tmp_name);
                $stmtImg = $conn->prepare("INSERT INTO proyecto_imagenes (id_proyecto, imagen) VALUES (?, ?)");
                $stmtImg->bindParam(1, $id_proyecto, PDO::PARAM_INT);
                $stmtImg->bindParam(2, $imgData, PDO::PARAM_LOB);
                $stmtImg->execute();
            }
        }
    }

    echo "Proyecto guardado correctamente";
} catch (Exception $e) {
    echo "Error al guardar el proyecto: " . $e->getMessage();
} 