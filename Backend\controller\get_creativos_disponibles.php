<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: application/json');

require_once __DIR__ . '/../models/conexion.php';

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $query = "SELECT id_pcreativa, nombre, apellido, titulo_profesional, foto_perfil 
              FROM perfil_creativo 
              WHERE estado_perfil = 1";

    $params = [];

    
    if (isset($_GET['categoria']) && !empty($_GET['categoria'])) {
        $query .= " AND id_categoria = ?";
        $params[] = $_GET['categoria'];
    }

    $query .= " ORDER BY nombre ASC";

    $stmt = $conn->prepare($query);
    $stmt->execute($params);
    $creativos = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($creativos as &$creativo) {
        if ($creativo['foto_perfil']) {
            $creativo['foto_perfil'] = 'data:image/jpeg;base64,' . base64_encode($creativo['foto_perfil']);
        } else {
            $creativo['foto_perfil'] = null;
        }
    }

    echo json_encode([
        'exito' => true,
        'creativos' => $creativos
    ]);

} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'exito' => false,
        'error' => 'Error al obtener los creativos disponibles: ' . $e->getMessage()
    ]);
}
