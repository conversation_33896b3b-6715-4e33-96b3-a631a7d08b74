<?php
header('Content-Type: application/json');
require_once __DIR__ . '/../models/conexion.php';

$token = $_GET['token'] ?? null;

if (!$token) {
    echo json_encode(['exito' => false, 'error' => 'No se proporcionó un token']);
    exit;
}

try {
    $stmt = $conn->prepare("
        SELECT c.nombre, c.telefono, c.email, c.descripcion, c.fechainicio, c.fechatermino, t.usado_en
        FROM tokens_contacto t
        JOIN contactos c ON t.id_contacto = c.id
        WHERE t.token = ?
    ");
    $stmt->execute([$token]);
    $contacto = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$contacto) {
        echo json_encode(['exito' => false, 'error' => 'Token inválido']);
    } elseif ($contacto['usado_en']) {
        echo json_encode(['exito' => false, 'error' => 'Este enlace ya fue utilizado']);
    } else {
        // Remover el campo usado_en del resultado
        unset($contacto['usado_en']);
        echo json_encode(['exito' => true, 'contacto' => $contacto]);
    }
} catch (PDOException $e) {
    echo json_encode(['exito' => false, 'error' => 'Error de base de datos: ' . $e->getMessage()]);
}
?> 