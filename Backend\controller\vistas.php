<?php
header('Content-Type: application/json');
$archivo = __DIR__ . '/log_estadistica.json';

if (file_exists($archivo)) {
    $contenido = file_get_contents($archivo);
    $datos = json_decode($contenido, true);

    if ($datos !== null && is_array($datos) && count($datos) > 0) {
        $entrada = $datos[0];
        $visitas = isset($entrada['visitas']) ? $entrada['visitas'] : 0;
        $fecha = isset($entrada['fecha']) ? $entrada['fecha'] : '';
        echo json_encode(['exito' => true, 'visitas' => $visitas, 'fecha' => $fecha]);
    } else {
        echo json_encode(['exito' => false, 'error' => 'No hay datos']);
    }
} else {
    echo json_encode(['exito' => false, 'error' => 'Archivo no encontrado']);
}