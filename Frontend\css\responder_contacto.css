body {
    font-family: Arial, sans-serif;
    background: #f8fafc;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 2rem;
}

.contenedor {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 600px;
}

.dato {
    font-size: 1rem;
    font-weight: bold;
    color: #007bff;
}

input, select {
    width: 100%;
    padding: 0.5rem;
    margin: 0.5rem 0 1rem;
    border-radius: 6px;
    border: 1px solid #ccc;
    box-sizing: border-box;
}

input:focus, select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

label {
    font-weight: bold;
    color: #333;
    display: block;
    margin-top: 1rem;
}

button {
    padding: 0.75rem 1.5rem;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-top: 1rem;
}

button:hover {
    background-color: #0056b3;
}

button:active {
    transform: translateY(1px);
}

.extra {
    display: none;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

#error-container {
    text-align: center;
}

#error-message {
    color: #dc3545;
    font-weight: bold;
    margin: 1rem 0;
}

#contacto-container {
    animation: slideIn 0.5s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .contenedor {
        padding: 1rem;
        margin: 1rem;
    }
    
    body {
        padding: 1rem;
    }
    
    input, select, button {
        font-size: 16px; /* Prevents zoom on iOS */
    }
} 