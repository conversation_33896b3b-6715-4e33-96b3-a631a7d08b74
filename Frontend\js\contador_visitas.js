
document.addEventListener('DOMContentLoaded', function() {
  fetch(`${window.API_URL_PHP}controller/get_visitas.php`)
    .then(res => res.json())
    .then(data => {
    if (data.exito) {
      document.getElementById('visitas-landing').textContent = data.visitas;
      document.getElementById('fecha-landing').textContent = data.fecha;
    } else {
      document.getElementById('visitas-landing').textContent = 'Error';
      document.getElementById('fecha-landing').textContent = 'Error';
    }
  })
  .catch(() => {
    document.getElementById('visitas-landing').textContent = 'Error de conexión';
    document.getElementById('fecha-landing').textContent = 'Error de conexión';
  }); 
})