<?php
// guardar_respuesta.php

require_once __DIR__ . '/../models/conexion.php';

// Habilitar reporte de errores para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['exito' => false, 'error' => 'Método no permitido']);
    exit;
}

// Log de datos recibidos
error_log("Datos POST recibidos: " . print_r($_POST, true));

$token = $_POST['token'] ?? null;
$fecha1 = $_POST['fecha1'] ?? null;
$fecha2 = !empty($_POST['fecha2']) ? $_POST['fecha2'] : null;
$fecha3 = !empty($_POST['fecha3']) ? $_POST['fecha3'] : null;
$modo   = $_POST['modo_contacto'] ?? null;
$direccion = !empty($_POST['direccion']) ? $_POST['direccion'] : null;
$link = !empty($_POST['link_videollamada']) ? $_POST['link_videollamada'] : null;
$apoyo = !empty($_POST['tipo_apoyo']) ? $_POST['tipo_apoyo'] : null;

if (!$token || !$fecha1 || !$modo) {
    http_response_code(400);
    echo json_encode(['exito' => false, 'error' => 'Faltan datos obligatorios']);
    exit;
}

try {
    // Verificar existencia del token
    $stmt = $conn->prepare("SELECT id_contacto, usado_en FROM tokens_contacto WHERE token = ?");
    $stmt->execute([$token]);
    $tokenData = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$tokenData) {
        throw new Exception("Token inválido");
    }

    if ($tokenData['usado_en']) {
        throw new Exception("Este token ya ha sido utilizado");
    }

    $id_contacto = $tokenData['id_contacto'];

    // Guardar respuesta en tabla respuestas_contacto
    $stmt = $conn->prepare("INSERT INTO respuestas_contacto 
        (id_contacto, fecha_opcion_1, fecha_opcion_2, fecha_opcion_3, modo_contacto, detalle_contacto, link_videollamada, tipo_apoyo)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)");

    $resultado = $stmt->execute([
        $id_contacto,
        $fecha1,
        $fecha2,
        $fecha3,
        $modo,
        $direccion,
        $link,
        $apoyo
    ]);

    if (!$resultado) {
        throw new Exception("Error al insertar en la base de datos");
    }

    // Marcar token como usado
    $stmt = $conn->prepare("UPDATE tokens_contacto SET usado_en = NOW() WHERE token = ?");
    $stmt->execute([$token]);

    // Cambiar estado a 'Match Inicial'
    $stmt = $conn->prepare("UPDATE contactos SET estado = 'Match Inicial' WHERE id = ?");
    $stmt->execute([$id_contacto]);
    
    // Log de éxito
    error_log("Respuesta guardada exitosamente para contacto ID: " . $id_contacto);

    // --- Llamar a enviar_respuesta.php para enviar el correo al cliente ---
    $_POST = [
        'id_contacto' => $id_contacto,
        'fecha_opcion_1' => $fecha1,
        'fecha_opcion_2' => $fecha2,
        'fecha_opcion_3' => $fecha3,
        'modo_contacto' => $modo,
        'detalle_contacto' => $direccion,
        'link_videollamada' => $link,
        'tipo_apoyo' => $apoyo
    ];
    ob_start();
    include(__DIR__ . '/enviar_respuesta.php');
    $output = ob_get_clean();
    error_log('Resultado de enviar_respuesta.php: ' . $output);
    // --- Fin llamada correo ---

    // Devolver respuesta JSON exitosa
    echo json_encode(['exito' => true, 'mensaje' => 'Respuesta guardada correctamente']);
    
} catch (Exception $e) {
    error_log("Error en post_guardar_contacto.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['exito' => false, 'error' => $e->getMessage()]);
}
