// Funcionalidad del menú hamburguesa
function initNavbar() {
    const navbarToggle = document.querySelector('.navbar-toggle');
    const navbarMenu = document.querySelector('.navbar-menu');
    const navbarLinks = document.querySelectorAll('.navbar-menu a');


    if (!navbarToggle || !navbarMenu) {
        return;
    }

    
    navbarToggle.addEventListener('click', function() {
        navbarToggle.classList.toggle('active');
        navbarMenu.classList.toggle('active');
    });

    
    navbarLinks.forEach(link => {
        link.addEventListener('click', function() {
            if (navbarToggle && navbarMenu) {
                navbarToggle.classList.remove('active');
                navbarMenu.classList.remove('active');
            }
        });
    });

   
    document.addEventListener('click', function(event) {
        if (!navbarToggle || !navbarMenu) return;

        const isClickInsideNav = navbarToggle.contains(event.target) || navbarMenu.contains(event.target);

        if (!isClickInsideNav && navbarMenu.classList.contains('active')) {
            navbarToggle.classList.remove('active');
            navbarMenu.classList.remove('active');
        }
    });

    
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768 && navbarToggle && navbarMenu) {
            navbarToggle.classList.remove('active');
            navbarMenu.classList.remove('active');
        }
    });
}

document.addEventListener('DOMContentLoaded', function() {
    // Esperar un poco para que el header se cargue
    setTimeout(initNavbar, 100);

    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                const navbarToggle = document.querySelector('.navbar-toggle');
                if (navbarToggle) {
                    initNavbar();
                    observer.disconnect();
                }
            }
        });
    });

    const headerElement = document.getElementById('header');
    if (headerElement) {
        observer.observe(headerElement, { childList: true, subtree: true });
    }
});
