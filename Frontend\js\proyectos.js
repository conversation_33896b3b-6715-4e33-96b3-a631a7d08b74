document.addEventListener('DOMContentLoaded', function () {
    cargarProyectos();
});

async function cargarProyectos() {
    try {
        const response = await fetch(`${window.API_URL_PHP}controller/get_proyecto.php`);
        const data = await response.json();

        if (data.exito) {
            mostrarProyectos(data.proyectos);
        } else {
            mostrarError('No se encontraron proyectos.');
        }
    } catch (error) {
        console.error('Error al cargar los proyectos:', error);
        mostrarError('Error al cargar los proyectos.');
    }
}

function mostrarProyectos(proyectos) {
    const contenedor = document.querySelector('.projects-grid');
    contenedor.innerHTML = '';

    if (proyectos.length === 0) {
        contenedor.innerHTML = '<p class="no-projects">No hay proyectos disponibles.</p>';
        return;
    }

    proyectos.forEach((proyecto, index) => {
        const card = document.createElement('div');
        card.className = 'project-card';
        card.style.animationDelay = `${index * 0.1}s`;

        const imgSrc = proyecto.imagen_base64
            ? `data:image/jpeg;base64,${proyecto.imagen_base64}`
            : '../assets/default-project.jpg';

        card.innerHTML = `
            <div class="project-image">
                <img src="${imgSrc}" alt="${proyecto.titulo_proyecto}" class="project-img">
            </div>
            <div class="project-content">
                <h3 class="project-title">${proyecto.titulo_proyecto}</h3>
                <p class="project-description">${proyecto.descripcion}</p>
                <button class="project-button" onclick="verProyecto(${proyecto.id_proyectos})">Ver más</button>
            </div>
        `;

        contenedor.appendChild(card);
    });
}

function mostrarError(mensaje) {
    const contenedor = document.querySelector('.projects-grid');
    contenedor.innerHTML = `<p class="error">${mensaje}</p>`;
}

function verProyecto(id) {
    window.location.href = `proyecto_detalle.html?id=${id}`;
}
