<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: application/json'); 
require_once __DIR__ . '/conexion.php';

$data = json_decode(file_get_contents("php://input"), true);

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    
    $check = $conn->prepare("SELECT COUNT(*) FROM categorias WHERE nombre_categoria = ?");
    $check->execute([$data['nombre_categoria']]);
    
    if ($check->fetchColumn() > 0) {
        http_response_code(400);
        echo json_encode([
            'exito' => false,
            'error' => 'Esta categoría ya existe'
        ]);
        exit();
    }
    
    $stmt = $conn->prepare("INSERT INTO categorias (nombre_categoria) VALUES (?)");
    $stmt->execute([$data['nombre_categoria']]);
    
    $categoria_id = $conn->lastInsertId();
    
    echo json_encode([
        'exito' => true,
        'mensaje' => 'Categoría creada correctamente',
        'id_categoria' => $categoria_id
    ]);
    
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'exito' => false, 
        'error' => 'Error al crear la categoría: ' . $e->getMessage()
    ]);
}
?>
