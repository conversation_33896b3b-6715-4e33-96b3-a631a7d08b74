<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../models/conexion.php';

try {
    // Usar la conexión ya establecida en conexion.php
    global $conn;
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $id_contacto = isset($_GET['id_contacto']) ? (int)$_GET['id_contacto'] : 0;
        
        if ($id_contacto <= 0) {
            echo json_encode(['error' => 'ID de contacto inválido']);
            exit;
        }

        $stmt = $conn->prepare("
            SELECT 
                id,
                id_contacto,
                fecha_opcion_1,
                fecha_opcion_2,
                fecha_opcion_3,
                modo_contacto,
                detalle_contacto,
                creado_en,
                link_videollamada,
                tipo_apoyo
            FROM respuestas_contacto 
            WHERE id_contacto = ?
            ORDER BY creado_en DESC
        ");
        
        $stmt->execute([$id_contacto]);
        $respuestas = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode($respuestas);
    } else {
        echo json_encode(['error' => 'Método no permitido']);
    }

} catch (PDOException $e) {
    echo json_encode(['error' => 'Error de base de datos: ' . $e->getMessage()]);
} catch (Exception $e) {
    echo json_encode(['error' => 'Error general: ' . $e->getMessage()]);
}
?>
