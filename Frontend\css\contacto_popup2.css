.popup-container {
  display: none;
  position: fixed;
  top: 0; left: 0;
  width: 100vw; height: 100vh;
  background-color: rgba(0, 0, 0, 0.6);
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.popup-content {
  background-color: #fff;
  border-radius: 12px;
  padding: 25px;
  width: 90%;
  max-width: 480px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.25);
  position: relative;
}

.cerrar-popup {
  position: absolute;
  top: 15px;
  right: 20px;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.form-group {
  margin-bottom: 15px;
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
}

input, textarea {
  width: 100%;
  padding: 8px;
  box-sizing: border-box;
  border: 1px solid #ddd;
  border-radius: 6px;
}

textarea {
  resize: vertical;
}



.cerrar-btn {
  background-color: #ff7514;
  margin-top: 10px;
}

.mensaje-container {
  margin-top: 15px;
  padding: 10px;
  border-radius: 6px;
  display: none;
  font-size: 14px;
}

.mensaje-exito {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.mensaje-error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.popup-content.contacto-datos {
  background-color: #1a1a1a;
  color: white;
  text-align: center;
}

.mensaje-no-disponible {
  padding: 12px;
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 5px;
  margin-bottom: 15px;
  text-align: center;
  font-family: 'Space Grotesk', sans-serif;
}

#recomendaciones-container h4 {
  margin-bottom: 16px; 
  margin-top: 8px;
}

.card-creativo {
  display: flex;
  align-items: center;
  gap: 14px;
  margin-bottom: 16px;
  padding: 14px;
  border: 2px solid #ff7514;
  border-radius: 10px;
  background-color: rgba(26, 26, 26, 0.5);
  backdrop-filter: blur(6px);
  transition: transform 0.2s, box-shadow 0.2s, background-color 0.3s;
}

.card-creativo:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 117, 20, 0.4);
  background-color: rgba(34, 34, 34, 0.5);
}

.card-creativo > div {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1; 
}

.card-creativo strong {
  color: white;
  font-size: 1em;
}

.card-creativo small {
  color: #ff7514;
  display: block;
  margin-top: 3px;
}

.foto-creativo {
  width: 55px;
  height: 55px;
  object-fit: cover;
  border-radius: 50%;
  border: 2px solid #ff7514;
}

.btn-ver-perfil {
  margin-top: 6px;
  padding: 6px 12px;
  font-size: 0.9em;
  cursor: pointer;
  background-color: #ff7514;
  color: white;
  border: none;
  border-radius: 20px;
  font-family: 'Space Grotesk', sans-serif;
  transition: all 0.3s ease;
}

.btn-ver-perfil:hover {
  background-color: #e66700;
}
