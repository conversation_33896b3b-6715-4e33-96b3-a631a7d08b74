:root {
  --primary: #ff6b35;
  --primary-dark: #ff5722;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Projects Gallery Styles */
.projects-gallery {
  padding: 10rem 0;
  background-color: #0a0a0a;
  position: relative;
  overflow: hidden;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.projects-header {
  text-align: center;
  margin-bottom: 6rem;
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease forwards;
}

.projects-title {
  font-size: 4rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1.5rem;
  font-family: 'Space Grotesk', sans-serif;
}

.projects-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.7);
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
  font-family: 'Space Grotesk', sans-serif;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 3rem;
}

.project-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.5s ease;
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease forwards;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.project-card:hover {
  transform: translateY(-10px) scale(1.02);
  background: rgba(255, 255, 255, 0.05);
  box-shadow: 0 20px 40px rgba(255, 107, 53, 0.1);
}

.project-image {
  position: relative;
  overflow: hidden;
  height: 250px;
  background: rgba(255, 255, 255, 0.02);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 15px 15px 0 0;
}

.project-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: all 0.5s ease;
  filter: brightness(0.9) contrast(1.1);
  padding: 15px;
}

.project-card:hover .project-img {
  transform: scale(1.02);
  filter: brightness(1) contrast(1.2);
}

.project-content {
  padding: 2rem;
}

.project-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #ff6b35;
  margin-bottom: 1rem;
  font-family: 'Space Grotesk', sans-serif;
}

.project-description {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 2rem;
  font-size: 0.95rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-family: 'Space Grotesk', sans-serif;
}

.project-button {
  display: inline-flex;
  align-items: center;
  background: #ff6b35;
  color: white;
  padding: 0.8rem 2rem;
  border-radius: 100px;
  font-weight: 600;
  font-size: 0.9rem;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Space Grotesk', sans-serif;
  position: relative;
  overflow: hidden;
}

.project-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.project-button:hover {
  background: #ff5722;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 53, 0.3);
}

.project-button:hover::before {
  transform: translateX(100%);
}

.cargando,
.error,
.no-projects {
  grid-column: 1 / -1;
  text-align: center;
  font-size: 1.2rem;
  color: #ff6b35;
  padding: 2rem 0;
  font-family: 'Space Grotesk', sans-serif;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .projects-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2.5rem;
  }

  .projects-title {
    font-size: 3rem;
  }
}

@media (max-width: 768px) {
  .projects-gallery {
    padding: 6rem 0;
  }

  .projects-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .projects-title {
    font-size: 2.5rem;
  }

  .projects-subtitle {
    font-size: 1.1rem;
  }

  .project-content {
    padding: 1.5rem;
  }

  .project-title {
    font-size: 1.5rem;
  }

  .container {
    padding: 0 1rem;
  }
}

@media (max-width: 480px) {
  .projects-header {
    margin-bottom: 4rem;
  }

  .projects-title {
    font-size: 2rem;
  }

  .project-button {
    padding: 0.7rem 1.5rem;
    font-size: 0.85rem;
  }
}

/* Global Styles */
html, body {
  margin: 0;
  padding: 0;
  background-color: #0a0a0a;
  font-family: 'Space Grotesk', sans-serif;
  color: white;
  overflow-x: hidden;
}

/* Navbar Styles */
.navbar {
  position: sticky;
  top: 0;
  width: 100%;
  background-color: #0a0a0a;
  border-bottom: 3px solid var(--primary);
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0,0,0,0.7);
  font-family: 'Space Grotesk', sans-serif;
}

.navbar-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.navbar-logo {
  color: var(--primary);
  font-weight: 700;
  font-size: 1.8rem;
  cursor: pointer;
  user-select: none;
}

.navbar-menu {
  list-style: none;
  display: flex;
  gap: 2rem;
}

.navbar-menu li a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.navbar-menu li a:hover,
.navbar-menu li a:focus {
  color: var(--primary);
}

.navbar-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 0.5rem;
}

.navbar-toggle span {
  width: 25px;
  height: 3px;
  background-color: var(--primary);
  margin: 3px 0;
  transition: 0.3s;
  border-radius: 2px;
}

.navbar-toggle.active span:nth-child(1) {
  transform: rotate(-45deg) translate(-5px, 6px);
}

.navbar-toggle.active span:nth-child(2) {
  opacity: 0;
}

.navbar-toggle.active span:nth-child(3) {
  transform: rotate(45deg) translate(-5px, -6px);
}

@media (max-width: 768px) {
  .navbar-toggle {
    display: flex;
  }

  .navbar-menu {
    position: fixed;
    top: 70px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 70px);
    background-color: #0a0a0a;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-top: 2rem;
    transition: left 0.3s ease;
    gap: 1.5rem;
  }

  .navbar-menu.active {
    left: 0;
  }

  .navbar-menu li {
    width: 100%;
    text-align: center;
  }

  .navbar-menu li a {
    display: block;
    padding: 1rem;
    font-size: 1.2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* Footer Styles */
.footer {
  background-color: #0a0a0a;
  padding: 1.5rem 2rem 1rem;
  color: rgba(255, 255, 255, 0.8);
  font-family: 'Space Grotesk', sans-serif;
  border-top: 2px solid var(--primary);
  margin-top: 2rem;
}

.footer-grid {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 0 1rem;
}

.footer-left {
  text-align: left;
  flex: 1 1 250px;
  min-width: 0;
}

.footer-right {
  text-align: right;
  flex: 1 1 250px;
  min-width: 0;
}

.footer-left h2 {
  color: var(--primary);
  font-size: 1.8rem;
  margin-bottom: 0.3rem;
}

.footer-left p {
  font-size: 0.9rem;
  color: rgba(255,255,255,0.6);
}

.footer-right p {
  margin: 0.2rem 0;
  font-size: 0.95rem;
}

.footer-right a {
  color: var(--primary);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-right a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Navbar ajustado con fondo #111 */
html, body {
  margin: 0;
  padding: 0;
  background-color: #111;
  font-family: 'Space Grotesk', sans-serif;
  color: white;
}

.footer {
  background-color: #111;
  padding: 4rem 2rem 2.5rem;
  color: rgba(255, 255, 255, 0.8);
  font-family: 'Space Grotesk', sans-serif;
  border-top: 3px solid var(--primary); /* Línea separadora naranja */
  margin-top: 4rem;
}

.footer-grid {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  max-width: 100vw;
  width: 100vw;
  margin: 0;
  flex-wrap: wrap;
  gap: 1.5rem;
  padding-left: 0;
  padding-right: 0;
}

.footer-left {
  text-align: left;
  flex: 1 1 300px;
}

.footer-right {
  text-align: right;
  flex: 1 1 300px;
}

.footer-left h2 {
  color: var(--primary);
  font-size: 1.8rem;
  margin-bottom: 0.3rem;
}

.footer-left p {
  font-size: 0.9rem;
  color: rgba(255,255,255,0.6);
}

.footer-right p {
  margin: 0.2rem 0;
  font-size: 0.95rem;
}

.footer-right a {
  color: var(--primary);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-right a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

  @media (max-width: 600px) {
    .footer-grid {
      flex-direction: column;
      text-align: center;
      gap: 1rem;
    }

    .footer-right p {
      margin: 0.3rem 0;
    }

    
      .creatives-grid {
   grid-template-columns: minmax(200px, 1fr);
  }
    
  }

.navbar {
  position: sticky;
  top: 0;
  width: 100%;
  background-color: #111;
  border-bottom: 3px solid var(--primary); /* Línea separadora naranja */
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0,0,0,0.7);
  font-family: 'Space Grotesk', sans-serif;
}

.navbar-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.navbar-logo {
  color: var(--primary);
  font-weight: 700;
  font-size: 1.8rem;
  cursor: pointer;
  user-select: none;
}

.navbar-menu {
  list-style: none;
  display: flex;
  gap: 2rem;
}

.navbar-menu li a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.navbar-menu li a:hover,
.navbar-menu li a:focus {
  color: var(--primary);
}

/* Menú hamburguesa */
.navbar-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 0.5rem;
}

.navbar-toggle span {
  width: 25px;
  height: 3px;
  background-color: var(--primary);
  margin: 3px 0;
  transition: 0.3s;
  border-radius: 2px;
}

/* Animación del icono hamburguesa */
.navbar-toggle.active span:nth-child(1) {
  transform: rotate(-45deg) translate(-5px, 6px);
}

.navbar-toggle.active span:nth-child(2) {
  opacity: 0;
}

.navbar-toggle.active span:nth-child(3) {
  transform: rotate(45deg) translate(-5px, -6px);
}

/* Responsive */
@media (max-width: 768px) {
  .navbar-toggle {
    display: flex;
  }

  .navbar-menu {
    position: fixed;
    top: 70px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 70px);
    background-color: #111;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-top: 2rem;
    transition: left 0.3s ease;
    gap: 1.5rem;
  }

  .navbar-menu.active {
    left: 0;
  }

  .navbar-menu li {
    width: 100%;
    text-align: center;
  }

  .navbar-menu li a {
    display: block;
    padding: 1rem;
    font-size: 1.2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
}

.btn-proyecto {
  display: block;
  width: 80%;
  padding: 0.8rem;
  background-color: #ff6b35; /* Color naranja */
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 1rem auto; /* Centra el botón horizontalmente */
  text-align: center;
}