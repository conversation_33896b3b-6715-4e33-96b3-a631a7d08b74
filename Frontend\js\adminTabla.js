function mostrarTabla(contactos, admin = false) {
  const container = admin ? document.getElementById('admin-tabla-container') : document.getElementById('tabla-container');
  let html = `
    <table class="contactos-table">
      <thead><tr>
        <th>ID</th><th>Nombre</th><th>Teléfono</th><th>Email</th><th>Descripción</th>
        <th>Fecha inicio</th><th>Fecha término</th><th>Creativo</th><th>Estado</th>
      </tr></thead><tbody>`;

  contactos.forEach(c => {
    html += `<tr>
      <td>${c.id}</td>
      <td>${c.nombre}</td>
      <td>${c.telefono || '-'}</td>
      <td>${c.email || '-'}</td>
      <td>${c.descripcion || '-'}</td>
      <td>${c.fechainicio || '-'}</td>
      <td>${c.fechatermino || '-'}</td>
      <td>${(c.nombre_creativo || '-') + (c.apellido_creativo ? ' ' + c.apellido_creativo : '')}</td>
      <td>
        <select class="estado-select" data-id="${c.id}">
          ${[
            'Sin Estado', 'Contacto Inicial', 'Match Inicial',
            'Match Confirmado', 'Match Rechazado', 'Match Inactivo', 'Match Finalizado'
          ].map(e => `<option value="${e}" ${c.estado === e ? 'selected' : ''}>${e}</option>`).join('')}
        </select>
      </td>
    </tr>`;
  });

  html += '</tbody></table>';
  container.innerHTML = html;

  // Función para aplicar colores según el estado
  function aplicarColorEstado(select) {
    // Remover todas las clases de estado
    select.classList.remove('inicial', 'minicial', 'confirmado', 'rechazado', 'inactivo', 'finalizado');

    // Agregar clase según el valor
    const valor = select.value;
    if (valor === 'Contacto Inicial') {
      select.classList.add('inicial');
    } else if (valor === 'Match Inicial') {
      select.classList.add('minicial');
    } else if (valor === 'Match Confirmado') {
      select.classList.add('confirmado');
    } else if (valor === 'Match Rechazado') {
      select.classList.add('rechazado');
    } else if (valor === 'Match Inactivo') {
      select.classList.add('inactivo');
    } else if (valor === 'Match Finalizado') {
      select.classList.add('finalizado');
    }
  }

  document.querySelectorAll('.estado-select').forEach(select => {
    select.setAttribute('data-prev', select.value);
    // Aplicar color inicial
    aplicarColorEstado(select);

    select.addEventListener('change', function () {
      const id = this.getAttribute('data-id');
      const estado = this.value;
      const prev = this.getAttribute('data-prev');

      modalConfirm.style.display = 'flex';

      btnConfirm.onclick = () => {
        fetch(urlUpdateEstadoContacto, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ id, estado })
        })
          .then(res => res.json())
          .then(resp => {
            if (!resp.exito) {
              alert('Error al guardar el estado');
              this.value = prev;
            } else {
              this.setAttribute('data-prev', estado);
              // Aplicar nuevo color
              aplicarColorEstado(this);
            }
            modalConfirm.style.display = 'none';
          })
          .catch(() => {
            alert('Error de red');
            this.value = prev;
            modalConfirm.style.display = 'none';
          });
      };

      btnCancel.onclick = () => {
        this.value = prev;
        modalConfirm.style.display = 'none';
      };
    });
  });
}
