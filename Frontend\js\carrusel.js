class CarruselCreativos {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.currentSlide = 0;
        this.cards = [];
        this.cardsPerView = window.innerWidth <= 768 ? 1 : window.innerWidth <= 1024 ? 2 : 3;
        this.autoPlayInterval = null;

        if (this.container) {
            this.createCarouselStructure();
            this.setupEventListeners();
            this.startAutoPlay();
        }
    }

    createCarouselStructure() {
        if (!this.container.querySelector('.creatives-title')) {
            const title = document.createElement('h2');
            title.className = 'creatives-title';
            title.textContent = 'Nuestros Creativos';
            this.container.insertBefore(title, this.container.firstChild);
        }

        const grid = this.container.querySelector('.creatives-grid');
        if (!grid) return;

        this.cards = Array.from(grid.querySelectorAll('.creative-card'));
        if (this.cards.length === 0) return;

        grid.classList.remove('creatives-grid');
        grid.classList.add('carousel-container');

        const track = document.createElement('div');
        track.className = 'carousel-track';
        track.id = 'carousel-track';

        this.cards.forEach(card => track.appendChild(card));
        grid.innerHTML = '';
        grid.appendChild(track);

        this.createControls();
        this.updateCarousel();
    }

    createControls() {
        // Remover controles existentes para evitar duplicados
        const existingControls = this.container.querySelector('.carousel-controls');
        if (existingControls) existingControls.remove();

        const controlsContainer = document.createElement('div');
        controlsContainer.className = 'carousel-controls';

        const prevBtn = document.createElement('button');
        prevBtn.className = 'carousel-btn carousel-prev';
        prevBtn.innerHTML = '‹';
        prevBtn.onclick = () => this.prev();

        const nextBtn = document.createElement('button');
        nextBtn.className = 'carousel-btn carousel-next';
        nextBtn.innerHTML = '›';
        nextBtn.onclick = () => this.next();

        const indicatorsContainer = document.createElement('div');
        indicatorsContainer.className = 'carousel-indicators';

        const totalSlides = Math.ceil(this.cards.length / this.cardsPerView);
        for (let i = 0; i < totalSlides; i++) {
            const indicator = document.createElement('div');
            indicator.className = 'carousel-indicator';
            if (i === 0) indicator.classList.add('active');
            indicator.onclick = () => this.goToSlide(i);
            indicatorsContainer.appendChild(indicator);
        }

        controlsContainer.appendChild(prevBtn);
        controlsContainer.appendChild(indicatorsContainer);
        controlsContainer.appendChild(nextBtn);
        this.container.appendChild(controlsContainer);
    }

    setupEventListeners() {
        window.addEventListener('resize', () => {
            const newCardsPerView = window.innerWidth <= 768 ? 1 : window.innerWidth <= 1024 ? 2 : 3;
            if (newCardsPerView !== this.cardsPerView) {
                this.cardsPerView = newCardsPerView;
                this.currentSlide = 0;
                this.createControls();
                this.updateCarousel();
            }
        });

        this.container.addEventListener('mouseenter', () => this.stopAutoPlay());
        this.container.addEventListener('mouseleave', () => this.startAutoPlay());

        let startX = 0;
        this.container.addEventListener('touchstart', (e) => startX = e.touches[0].clientX);
        this.container.addEventListener('touchend', (e) => {
            const diff = startX - e.changedTouches[0].clientX;
            if (Math.abs(diff) > 50) diff > 0 ? this.next() : this.prev();
        });
    }

    updateCarousel() {
        const track = document.getElementById('carousel-track');
        if (!track || this.cards.length === 0) return;

        // Calcular el ancho de una card incluyendo el gap
        const cardWidth = this.cards[0].offsetWidth;
        const gap = 48; // 3rem
        const slideWidth = (cardWidth + gap) * this.cardsPerView;
        const translateX = -this.currentSlide * slideWidth;

        track.style.transform = `translateX(${translateX}px)`;

        document.querySelectorAll('.carousel-indicator').forEach((indicator, index) => {
            indicator.classList.toggle('active', index === this.currentSlide);
        });

        const maxSlides = Math.ceil(this.cards.length / this.cardsPerView) - 1;
        const prevBtn = document.querySelector('.carousel-prev');
        const nextBtn = document.querySelector('.carousel-next');
        if (prevBtn && nextBtn) {
            prevBtn.disabled = this.currentSlide <= 0;
            nextBtn.disabled = this.currentSlide >= maxSlides;
        }
    }

    next() {
        const maxSlides = Math.ceil(this.cards.length / this.cardsPerView) - 1;
        this.currentSlide = this.currentSlide < maxSlides ? this.currentSlide + 1 : 0;
        this.updateCarousel();
    }

    prev() {
        const maxSlides = Math.ceil(this.cards.length / this.cardsPerView) - 1;
        this.currentSlide = this.currentSlide > 0 ? this.currentSlide - 1 : maxSlides;
        this.updateCarousel();
    }

    goToSlide(slideIndex) {
        this.currentSlide = slideIndex;
        this.updateCarousel();
    }

    startAutoPlay() {
        this.stopAutoPlay();
        this.autoPlayInterval = setInterval(() => this.next(), 4000);
    }

    stopAutoPlay() {
        if (this.autoPlayInterval) {
            clearInterval(this.autoPlayInterval);
            this.autoPlayInterval = null;
        }
    }
}

window.inicializarCarrusel = function() {
    setTimeout(() => {
        const container = document.getElementById('lista-creativos');
        const cards = container?.querySelector('.creatives-grid')?.querySelectorAll('.creative-card');

        if (cards && cards.length > 0) {
            if (window.carruselCreativos) window.carruselCreativos.stopAutoPlay();
            window.carruselCreativos = new CarruselCreativos('lista-creativos');
        }
    }, 500);
};
