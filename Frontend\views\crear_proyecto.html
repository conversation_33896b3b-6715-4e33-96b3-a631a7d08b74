<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>Crear Proyecto</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: #111;
            padding: 2rem;
            color: white;
            min-height: 100vh;
        }
        .form-container {
            background: rgba(17, 17, 17, 0.95);
            max-width: 600px;
            margin: 0 auto;
            border-radius: 15px;
            border: 2px solid #ff6b35;
            box-shadow: 0 8px 32px rgba(255, 107, 53, 0.3);
            padding: 2.5rem;
        }
        h2 {
            color: #ff6b35;
            text-align: center;
            font-size: 2rem;
            margin-bottom: 2rem;
        }
        label {
            font-weight: bold;
            display: block;
            margin-top: 1.5rem;
            color: white;
            font-size: 1.1rem;
        }
        input, textarea {
            width: 100%;
            padding: 0.8rem;
            border-radius: 8px;
            border: 2px solid #333;
            margin-top: 0.5rem;
            background: #222;
            color: white;
            font-size: 1rem;
        }
        input:focus, textarea:focus {
            outline: none;
            border-color: #ff6b35;
            box-shadow: 0 0 10px rgba(255, 107, 53, 0.3);
        }
        button {
            background: #ff6b35;
            color: #fff;
            border: none;
            border-radius: 8px;
            padding: 1rem 2rem;
            font-weight: bold;
            margin-top: 2rem;
            cursor: pointer;
            width: 100%;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #ff5722;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
        }
        #mensaje-proyecto {
            margin-top: 1rem;
            text-align: center;
            font-weight: bold;
            padding: 1rem;
            border-radius: 8px;
        }
        .mensaje-exito {
            background: rgba(76, 175, 80, 0.2);
            color: #4caf50;
            border: 1px solid #4caf50;
        }
        .mensaje-error {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
            border: 1px solid #f44336;
        }

        /* Estilos para el área de subida de imágenes */
        .upload-area {
            border: 2px dashed #ff6b35;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            margin-top: 0.5rem;
            background: rgba(255, 107, 53, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            background: rgba(255, 107, 53, 0.1);
            border-color: #ff5722;
        }
        .upload-area.dragover {
            background: rgba(255, 107, 53, 0.15);
            border-color: #ff5722;
            transform: scale(1.02);
        }
        .upload-text {
            color: #ff6b35;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }
        .upload-hint {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.9rem;
        }
        #imagenes {
            display: none;
        }

        /* Preview de imágenes */
        .images-preview {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        .image-preview {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            background: #333;
            aspect-ratio: 1;
        }
        .image-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .remove-image {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(244, 67, 54, 0.8);
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            cursor: pointer;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .remove-image:hover {
            background: #f44336;
        }
        .image-count {
            color: #ff6b35;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h2>Agregar Proyecto</h2>
        <form id="form-proyecto" action="../../Backend/controller/crear_proyecto.php" method="POST" enctype="multipart/form-data">
            <label for="titulo_proyecto">Título del Proyecto:</label>
            <input type="text" id="titulo_proyecto" name="titulo_proyecto" required>

            <label for="descripcion">Descripción:</label>
            <textarea id="descripcion" name="descripcion" rows="3" required></textarea>

            <label for="id_pcreativa">ID del Creativo:</label>
            <input type="number" id="id_pcreativa" name="id_pcreativa" required>

            <label for="imagenes">Imágenes del Proyecto:</label>
            <div class="upload-area" onclick="document.getElementById('imagenes').click()">
                <div class="upload-text">📸 Haz clic aquí o arrastra las imágenes</div>
                <div class="upload-hint">Puedes seleccionar múltiples imágenes (JPG, PNG, GIF)</div>
            </div>
            <input type="file" id="imagenes" name="imagenes[]" accept="image/*" multiple required>
            <div class="image-count" id="image-count">0 imágenes seleccionadas</div>
            <div class="images-preview" id="images-preview"></div>

            <button type="submit">Guardar Proyecto</button>
        </form>
        <div id="mensaje-proyecto"></div>
    </div>
    <script>
    let selectedFiles = [];

    // Manejo del área de drag & drop
    const uploadArea = document.querySelector('.upload-area');
    const fileInput = document.getElementById('imagenes');
    const previewContainer = document.getElementById('images-preview');
    const imageCount = document.getElementById('image-count');

    // Eventos de drag & drop
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = Array.from(e.dataTransfer.files);
        handleFiles(files);
    });

    // Evento de selección de archivos
    fileInput.addEventListener('change', (e) => {
        const files = Array.from(e.target.files);
        handleFiles(files);
    });

    function handleFiles(files) {
        // Filtrar solo imágenes
        const imageFiles = files.filter(file => file.type.startsWith('image/'));

        // Agregar nuevos archivos a la lista
        selectedFiles = [...selectedFiles, ...imageFiles];

        updatePreview();
        updateFileInput();
    }

    function updatePreview() {
        previewContainer.innerHTML = '';

        selectedFiles.forEach((file, index) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const previewDiv = document.createElement('div');
                previewDiv.className = 'image-preview';
                previewDiv.innerHTML = `
                    <img src="${e.target.result}" alt="Preview ${index + 1}">
                    <button type="button" class="remove-image" onclick="removeImage(${index})">×</button>
                `;
                previewContainer.appendChild(previewDiv);
            };
            reader.readAsDataURL(file);
        });

        // Actualizar contador
        imageCount.textContent = `${selectedFiles.length} imagen${selectedFiles.length !== 1 ? 'es' : ''} seleccionada${selectedFiles.length !== 1 ? 's' : ''}`;
    }

    function removeImage(index) {
        selectedFiles.splice(index, 1);
        updatePreview();
        updateFileInput();
    }

    function updateFileInput() {
        // Crear un nuevo DataTransfer para actualizar el input
        const dt = new DataTransfer();
        selectedFiles.forEach(file => dt.items.add(file));
        fileInput.files = dt.files;
    }

    // Envío del formulario
    document.getElementById('form-proyecto').addEventListener('submit', function(e) {
        e.preventDefault();

        if (selectedFiles.length === 0) {
            mostrarMensaje('Por favor selecciona al menos una imagen', 'error');
            return;
        }

        const form = e.target;
        const data = new FormData(form);

        // Mostrar mensaje de carga
        mostrarMensaje('Guardando proyecto...', 'info');

        fetch(form.action, {
            method: 'POST',
            body: data
        })
        .then(res => res.text())
        .then(msg => {
            mostrarMensaje(msg, 'exito');
            // Limpiar formulario
            form.reset();
            selectedFiles = [];
            updatePreview();
        })
        .catch(() => {
            mostrarMensaje('Error al guardar el proyecto', 'error');
        });
    });

    function mostrarMensaje(mensaje, tipo) {
        const mensajeDiv = document.getElementById('mensaje-proyecto');
        mensajeDiv.textContent = mensaje;
        mensajeDiv.className = `mensaje-${tipo}`;

        // Auto-ocultar después de 5 segundos si es éxito
        if (tipo === 'exito') {
            setTimeout(() => {
                mensajeDiv.textContent = '';
                mensajeDiv.className = '';
            }, 5000);
        }
    }
    </script>
</body>
</html> 