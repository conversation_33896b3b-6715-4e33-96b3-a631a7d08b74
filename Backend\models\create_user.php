<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: application/json'); 
require_once __DIR__ . '/conexion.php';

$data = json_decode(file_get_contents("php://input"), true);

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    
    $check = $conn->prepare("SELECT COUNT(*) FROM usuario WHERE correo_electronico = ?");
    $check->execute([$data['correo_electronico']]);
    
    if ($check->fetchColumn() > 0) {
        http_response_code(400);
        echo json_encode([
            'exito' => false,
            'error' => 'El correo electrónico ya está registrado'
        ]);
        exit();
    }
    
 
    $contrasena_hash = password_hash($data['contrasena'], PASSWORD_DEFAULT);
    
    $stmt = $conn->prepare("INSERT INTO usuario (correo_electronico, contrasena, tipo_usuario) VALUES (?, ?, ?)");
    $stmt->execute([
        $data['correo_electronico'],
        $contrasena_hash,
        $data['tipo_usuario']
    ]);
    
    $usuario_id = $conn->lastInsertId();
    
    echo json_encode([
        'exito' => true,
        'mensaje' => 'Usuario creado correctamente',
        'id_usuario' => $usuario_id
    ]);
    
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'exito' => false, 
        'error' => 'Error al crear el usuario: ' . $e->getMessage()
    ]);
}
