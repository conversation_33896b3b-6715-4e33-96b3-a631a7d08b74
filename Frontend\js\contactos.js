document.addEventListener('DOMContentLoaded', function () {
  const API_BASE = window.API_URL_PHP || '../../Backend/';

  // URLs globales
  window.urlContactos = `${API_BASE}controller/get_contactos.php`;
  window.urlRespuestas = `${API_BASE}controller/get_respuestas.php`;
  window.urlUpdateEstadoContacto = `${API_BASE}controller/update_estado_contacto.php`;
  window.urlPerfiles = `${API_BASE}controller/get_perfiles.php`;
  window.urlUpdatePerfil = `${API_BASE}controller/update_estado_perfil.php`;

  // Elementos
  const tarjetasContainer = document.getElementById('tarjetas-container');
  const tablaContainer = document.getElementById('tabla-container');
  const adminTablaContainer = document.getElementById('admin-tabla-container');
  const estadoPerfilesContainer = document.getElementById('estado-perfiles-container');

  const tabTarjetas = document.getElementById('tab-tarjetas');
  const tabTabla = document.getElementById('tab-tabla');
  const tabAdminTabla = document.getElementById('tab-admin-tabla');
  const tabEstadoPerfiles = document.getElementById('tab-estado-perfiles');

  window.modalConfirm = document.getElementById('confirm-modal');
  window.btnConfirm = document.getElementById('confirm-btn');
  window.btnCancel = document.getElementById('cancel-btn');

  window.modalDetalles = document.getElementById('detalles-modal');
  window.modalDetallesContent = document.getElementById('detalles-modal-content');

  window.contactosGlobal = [];

  // Tabs
  tabTarjetas.addEventListener('click', () => {
    tarjetasContainer.style.display = 'flex';
    tablaContainer.style.display = 'none';
    adminTablaContainer.style.display = 'none';
    estadoPerfilesContainer.style.display = 'none';
    document.getElementById('filter-tarjeta').style.display = 'block';
  });

  tabAdminTabla.addEventListener('click', () => {
    tarjetasContainer.style.display = 'none';
    tablaContainer.style.display = 'none';
    adminTablaContainer.style.display = 'block';
    estadoPerfilesContainer.style.display = 'none';
    document.getElementById('filter-tarjeta').style.display = 'none';
  });

  tabEstadoPerfiles.addEventListener('click', () => {
    tarjetasContainer.style.display = 'none';
    tablaContainer.style.display = 'none';
    adminTablaContainer.style.display = 'none';
    estadoPerfilesContainer.style.display = 'block';
    document.getElementById('filter-tarjeta').style.display = 'none';
    cargarPerfiles();
  });

  tabTabla?.addEventListener('click', () => {
    tarjetasContainer.style.display = 'none';
    tablaContainer.style.display = 'block';
    adminTablaContainer.style.display = 'none';
    estadoPerfilesContainer.style.display = 'none';
    document.getElementById('filter-tarjeta').style.display = 'none';
  });

  // Filtro de tarjetas
  const filtroEstado = document.getElementById('filtro-estado');
  filtroEstado?.addEventListener('change', function () {
    mostrarTarjetas(contactosGlobal, this.value);
  });

  // Carga inicial
  fetch(urlContactos)
    .then(res => res.json())
    .then(data => {
      contactosGlobal = data;
      mostrarTarjetas(data);
      mostrarTabla(data, false);
      mostrarTabla(data, true);
    });

  tabTarjetas.click();
});
