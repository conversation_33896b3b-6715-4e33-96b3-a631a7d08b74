<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: application/json');

require_once __DIR__ . '/../models/conexion.php';

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if (!isset($_GET['id'])) {
    http_response_code(400);
    echo json_encode([
        'exito' => false,
        'error' => 'No se proporcionó un ID de perfil'
    ]);
    exit();
}

$id = $_GET['id'];

try {
  
    $stmt = $conn->prepare("SELECT * FROM perfil_creativo WHERE id_pcreativa = ?");
    $stmt->execute([$id]);
    $perfil = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($perfil) {
       
        if ($perfil['foto_perfil']) {
            $perfil['foto_perfil'] = 'data:image/jpeg;base64,' . base64_encode($perfil['foto_perfil']);
        } else {
            $perfil['foto_perfil'] = null;
        }
        

        $stmtCategoria = $conn->prepare("SELECT nombre_categoria FROM categorias WHERE id_categoria = ?");
        $stmtCategoria->execute([$perfil['id_categoria']]);
        $categoria = $stmtCategoria->fetch(PDO::FETCH_ASSOC);
        
        if ($categoria) {
            $perfil['categoria'] = $categoria['nombre_categoria'];
        } else {
            $perfil['categoria'] = 'No especificada';
        }
        
        echo json_encode([
            'exito' => true,
            'perfil' => $perfil
        ]);
    } else {
        http_response_code(404);
        echo json_encode([
            'exito' => false,
            'error' => 'Perfil no encontrado'
        ]);
    }
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'exito' => false,
        'error' => 'Error al obtener el perfil: ' . $e->getMessage()
    ]);
}
