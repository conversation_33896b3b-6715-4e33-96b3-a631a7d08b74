# match-creativo

Este proyecto es un MVP para un sistema de scouting de creativos la maqueta inicial se encuentra en :

https://sites.google.com/view/scoutingcreativofloan/home

según esta base se descomponen los componentes y se comienza el desarrollo de un MVP de la funcionalidad 

04-02-2025
documentación proyecto en : https://docs.google.com/document/d/1rrVDrmdrGAmGVkvOnv6nffJ7jRFdhjVmymDv27e_Sv8/edit?usp=sharing

# Configuración del Proyecto

# Pasos para configurar el entorno local

1. Clona el repositorio
2. Ejecuta `composer install`
3. Copia el archivo `.env.ejemplo` a `.env`:
4. Edita el archivo `.env` con tus credenciales locales y de produccion
5. listo para comenzar

# Notas importantes
- Nunca subas el archivo `.env` al repositorio
- Mantén actualizado el `.env.ejemplo` si agregas nuevas variables de entorno

