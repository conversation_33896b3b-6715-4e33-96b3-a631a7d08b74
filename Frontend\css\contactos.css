body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    background: #f5f3f3;
}
.tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}
.tab-btn {
    background: #fff7f0;
    border: 2px solid #ff9800;
    border-radius: 8px;
    padding: 8px 20px;
    cursor: pointer;
    font-weight: 600;
    color: #ff9800;
    transition: background 0.2s, color 0.2s, border 0.2s;
}
.tab-btn.active {
    background: #ff9800;
    color: #fff;
    border: 2px solid #ff9800;
}
#tarjetas-container {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
    margin-bottom: 30px;
}
.contacto-card {
    background: #fff7f0;
    border-radius: 14px;
    box-shadow: 0 2px 8px rgba(255,152,0,0.10);
    border: 2px solid #ff9800;
    padding: 24px;
    min-width: 270px;
    max-width: 320px;
    flex: 1 1 270px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}
.ver-btn, .editar-btn, .eliminar-btn {
    background: #ff9800;
    color: #fff;
    border: none;
    border-radius: 6px;
    padding: 6px 16px;
    margin-right: 5px;
    cursor: pointer;
    font-weight: 600;
    transition: background 0.2s;
}
.ver-btn:hover, .editar-btn:hover, .eliminar-btn:hover {
    background: #fb8c00;
}
.editar-btn {
    background: #ffa726;
}
.eliminar-btn {
    background: #ff7043;
}
.contactos-table {
    width: 100%;
    border-collapse: collapse;
    background: #fff7f0;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 30px;
    border: 2px solid #ff9800;
}
.contactos-table th, .contactos-table td {
    padding: 12px 10px;
    border-bottom: 1px solid #ffe0b2;
    text-align: left;
}
.contactos-table th {
    background: #ff9800;
    color: #fff;
    font-weight: 700;
}
.modal {
  position: fixed;
  z-index: 9999;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal-content {
  background: #fff;
  border-radius: 12px;
  padding: 32px 40px;
  text-align: center;
  box-shadow: 0 2px 16px rgba(0,0,0,0.15);
}
.btn-confirmar {
  background: #ff9800;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 8px 24px;
  margin-right: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
}
.btn-confirmar:hover {
  background: #fb8c00;
}
.btn-cancelar {
  background: #ff7043;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 8px 24px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
}
.btn-cancelar:hover {
  background: #d32f2f;
} 

.inicial{
    background-color: yellow;
    border: 2px;
}
.minicial{
    background-color: #ff7300;
    border: 2px;
}
.confirmado{
    background-color: #00ff08;
    border: 2px;
}
.rechazado{
    background-color: #ff1100;
    border: 2px;
}
.inactivo{
    background-color: #000000;
    border: 2px;
}
.finalizado{
    background-color: #083ba8;
}

.estado-select {
    border: none;
    border-radius: 20px;
    padding: 4px 18px 4px 12px;
    font-weight: 700;
    font-size: 15px;
    background: #e0f7e9; 
    color: #1b5e20;      
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    cursor: pointer;
    min-width: 120px;
    text-align: center;
    box-shadow: 0 1px 4px rgba(0,0,0,0.07);
    transition: background 0.2s, color 0.2s;
    position: relative;
}

/* Flecha personalizada */
.estado-select {
    background-image: url("data:image/svg+xml;utf8,<svg fill='green' height='16' viewBox='0 0 24 24' width='16' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/></svg>");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 18px 18px;
    padding-right: 32px;
}

/* Colores según el valor seleccionado (solo visual, requiere JS para cambiar la clase) */
.estado-select.inicial {
    background: yellow;
    color: #000;
}
.estado-select.minicial {
    background: #ff7300;
    color: #fff;
}
.estado-select.confirmado {
    background: #00ff08;
    color: #000;
}
.estado-select.rechazado {
    background: #ff1100;
    color: #fff;
}
.estado-select.inactivo {
    background: #000000;
    color: #fff;
}
.estado-select.finalizado {
    background: #083ba8;
    color: #fff;
}

.volver-btn{
    display: inline-block;
    margin-left: auto;
    background-color: orange;
    color: white;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    border: none;
    position: absolute;
    right: 20px;
    top: 20px;

}

.estado-badge {
  display: inline-block;
  padding: 4px 10px;
  font-size: 12px;
  font-weight: bold;
  border-radius: 20px;
  margin-bottom: 10px;
  text-align: center;
  width: fit-content;
  text-transform: uppercase;
}

/* Colores personalizados por estado */
.sin-estado {
  background-color: #d3d3d3;
  color: #333;
}

.contacto-inicial {
  background-color: #e0f7fa;
  color: #00796b;
}

.match-inicial {
  background-color: #fff9c4;
  color: #f57f17;
}

.match-confirmado {
  background-color: #c8e6c9;
  color: #2e7d32;
}

.match-rechazado {
  background-color: #ffcdd2;
  color: #c62828;
}

.match-inactivo {
  background-color: #eeeeee;
  color: #757575;
}

.match-finalizado {
  background-color: #d1c4e9;
  color: #6a1b9a;
}

#filtro-estado {
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 14px;
  border: 1px solid #ccc;
}

/* Estilos para el botón "Ver más" */
.ver-mas-btn {
    background: #ff9800;
    color: #fff;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    cursor: pointer;
    font-weight: 600;
    transition: background 0.2s;
    margin-top: 10px;
    width: 100%;
}

.ver-mas-btn:hover {
    background: #fb8c00;
}

.card-actions {
    margin-top: auto;
    padding-top: 10px;
}

/* Estilos para el modal de detalles */
.detalles-modal-content {
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    width: 90%;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid #ff9800;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.modal-header h2 {
    margin: 0;
    color: #ff9800;
    font-size: 24px;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.2s;
}

.close:hover {
    color: #ff9800;
}

.respuesta-item {
    background: #fff7f0;
    border: 1px solid #ff9800;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.respuesta-item h3 {
    color: #ff9800;
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 18px;
}

.respuesta-details p {
    margin: 8px 0;
    line-height: 1.5;
}

.respuesta-details strong {
    color: #ff9800;
    font-weight: 600;
}

.respuesta-details a {
    color: #ff9800;
    text-decoration: none;
    word-break: break-all;
}

.respuesta-details a:hover {
    text-decoration: underline;
}

.no-respuestas {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 20px;
}

.estado-select.disponible {
    background-color: #28a745;
    color: white;
  }
  
  .estado-select.nodisponible {
    background-color: #dc3545;
    color: white;
  }
