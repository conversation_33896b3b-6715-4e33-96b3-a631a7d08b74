<?php
header('Content-Type: application/json');
require_once __DIR__ . '/../models/conexion.php';

$data = json_decode(file_get_contents('php://input'), true);
$id = $data['id'] ?? null;
$estado = $data['estado'] ?? null;

if ($id && $estado) {
    try {
        $stmt = $conn->prepare('UPDATE contactos SET estado = ? WHERE id = ?');
        $stmt->execute([$estado, $id]);
        echo json_encode(['exito' => true]);
    } catch (PDOException $e) {
        echo json_encode(['exito' => false, 'error' => $e->getMessage()]);
    }
} else {
    echo json_encode(['exito' => false, 'error' => 'Datos incompletos']);
} 