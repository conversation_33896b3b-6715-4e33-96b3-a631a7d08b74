/* Estilos para la página de perfil creativo */
body {
    font-family: 'Space Grotesk', sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
    color: #333;
}

/* Icono de estado de disponibilidad */
.estado-disponibilidad {
    width: 24px;
    height: 24px;
    vertical-align: middle;
    margin-left: 8px;
    object-fit: contain;
    cursor: help;
}

.volver {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 100;
}

.volver a {
    text-decoration: none;
    color: #333;
    background-color: #f8f8f8;
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: 500;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.volver a:hover {
    background-color: #ff7514;
    color: white;
}

.perfil-container {
    max-width: 800px;
    margin: 40px auto;
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.perfil-header {
    text-align: center;
    padding: 40px 20px;
    border-bottom: 1px solid #eee;
}

.perfil-foto {
    width: 120px;
    height: 120px;
    margin: 0 auto 20px;
    position: relative;
}

.perfil-foto img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    border: 3px solid #ff7514;
}

.perfil-header h1 {
    margin: 0 0 5px 0;
    font-size: 24px;
    color: #333;
}

.perfil-header p {
    margin: 0 0 20px 0;
    font-size: 16px;
    color: #666;
}

.btn-contactar {
    background-color: #ff7514;
    color: white;
    border: none;
    padding: 10px 25px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-contactar:hover {
    background-color: #e66700;
    transform: translateY(-2px);
}

.perfil-seccion {
    padding: 30px;
    border-bottom: 1px solid #eee;
}

.perfil-seccion:last-child {
    border-bottom: none;
}

.perfil-seccion h2 {
    margin: 0 0 20px 0;
    font-size: 20px;
    color: #333;
    position: relative;
    padding-bottom: 10px;
}

.perfil-seccion h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: #ff7514;
}

.experiencia-item {
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
}

.experiencia-periodo {
    font-weight: 500;
    color: #ff7514;
    margin-bottom: 5px;
}

.experiencia-contenido {
    margin-left: 0;
}

.experiencia-contenido p {
    margin: 0;
    line-height: 1.6;
}

.proyectos-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: center;
}

/* Estilos para mensajes de error */
.error-mensaje {
    text-align: center;
    padding: 40px 20px;
}

.error-mensaje h2 {
    color: #e74c3c;
    margin-bottom: 15px;
}

.error-mensaje p {
    margin-bottom: 20px;
    color: #666;
}

.btn-volver {
    display: inline-block;
    background-color: #ff7514;
    color: white;
    text-decoration: none;
    padding: 10px 25px;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-volver:hover {
    background-color: #e66700;
    transform: translateY(-2px);
}

/* Estilos para la sección de proyectos */
.proyecto-card {
    background-color: #f9f9f9;
    border-radius: 8px;
    overflow: hidden;
    width: 100%;
    max-width: 300px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.proyecto-card:hover {
    transform: translateY(-5px);
}

.proyecto-imagen {
    width: 100%;
    height: 180px;
    overflow: hidden;
}

.proyecto-imagen img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.proyecto-card:hover .proyecto-imagen img {
    transform: scale(1.05);
}

.proyecto-info {
    padding: 15px;
}

.proyecto-info h3 {
    margin: 0 0 10px 0;
    font-size: 18px;
    color: #333;
}

.proyecto-info p {
    margin: 0;
    font-size: 14px;
    color: #666;
    line-height: 1.4;
}

.ver-proyecto {
    display: inline-block;
    margin-top: 15px;
    color: #ff7514;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: color 0.3s ease;
}

.ver-proyecto:hover {
    color: #e66700;
}

/* Estilos para dispositivos móviles */
@media (max-width: 768px) {
    .perfil-container {
        margin: 20px;
        width: auto;
    }
    
    .perfil-header {
        padding: 30px 15px;
    }
    
    .perfil-seccion {
        padding: 20px;
    }
    
    .proyecto-card {
        max-width: 100%;
    }
}

/* Animaciones */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.perfil-header, .perfil-seccion {
    animation: fadeIn 0.5s ease forwards;
}

.perfil-seccion:nth-child(2) {
    animation-delay: 0.1s;
}

.perfil-seccion:nth-child(3) {
    animation-delay: 0.2s;
}

.perfil-seccion:nth-child(4) {
    animation-delay: 0.3s;
}
.proyecto-card {
    background: #fff3e0;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    padding: 16px;
    margin: 12px;
    display: inline-block;
    width: 220px;
    vertical-align: top;
    text-align: center;
}
.proyecto-img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 10px;
}