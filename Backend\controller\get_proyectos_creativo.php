<?php
header('Content-Type: application/json');
require_once __DIR__ . '/../models/conexion.php';

$id_pcreativa = $_GET['id_pcreativa'] ?? null;
if (!$id_pcreativa) {
    echo json_encode(['exito' => false, 'error' => 'Falta id_pcreativa']);
    exit;
}

$stmt = $conn->prepare("
    SELECT 
        p.id_proyectos,
        p.titulo_proyecto,
        p.descripcion,
        (
            SELECT TO_BASE64(imagen) 
            FROM proyecto_imagenes 
            WHERE id_proyecto = p.id_proyectos 
            LIMIT 1
        ) AS imagen_base64
    FROM 
        proyectos p
    WHERE 
        p.id_pcreativa = ?
");
$stmt->execute([$id_pcreativa]);
$proyectos = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Si quieres solo la primera imagen por proyecto, puedes filtrar en PHP aquí.

echo json_encode(['exito' => true, 'proyectos' => $proyectos]);